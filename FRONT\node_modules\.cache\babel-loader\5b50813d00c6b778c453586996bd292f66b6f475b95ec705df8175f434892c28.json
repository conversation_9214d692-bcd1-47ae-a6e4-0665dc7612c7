{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\nimport { useEffect, useRef } from 'react';\nimport AuthRoutes from './routes/AuthRoutes.jsx';\nimport UserRoutes from './routes/UserRoutes.jsx';\nimport AdminRoutes from './routes/AdminRoutes.jsx';\nimport PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';\nimport { NotificationProvider } from './context/NotificationContext';\nimport { PermissionsProvider } from './context/PermissionsContext';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\nimport '../src/assets/styles/custom.css';\nimport './App.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppRouter() {\n  _s();\n  const token = localStorage.getItem('token');\n  const role = localStorage.getItem('role');\n  const navigate = useNavigate();\n  const initialLoadRef = useRef(true);\n  useEffect(() => {\n    // Only redirect on initial load\n    if (initialLoadRef.current && token && window.location.pathname === '/') {\n      initialLoadRef.current = false;\n      if (role === 'trainee') {\n        navigate('/user/dashboard');\n      } else {\n        // For any other role (admin, trainer, etc.)\n        navigate('/admin/dashboard');\n      }\n    }\n  }, [token, role, navigate]);\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/public/courseDetails/:encodedId\",\n      element: /*#__PURE__*/_jsxDEV(PublicCourseDetails, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 63\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), !token && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 36\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/register\",\n        element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/forgot-password\",\n        element: /*#__PURE__*/_jsxDEV(ForgotPassword, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/forgot-password-otp\",\n        element: /*#__PURE__*/_jsxDEV(ForgotPasswordOTP, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 60\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/register-otp\",\n        element: /*#__PURE__*/_jsxDEV(RegisterOTP, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/reset-password\",\n        element: /*#__PURE__*/_jsxDEV(ResetPassword, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/active-account\",\n        element: /*#__PURE__*/_jsxDEV(ActiveAccountPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/error/401\",\n        element: /*#__PURE__*/_jsxDEV(Error401, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 45\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(PageNotFound, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 36\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), token && role === 'trainee' && /*#__PURE__*/_jsxDEV(Route, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(UserRoutes, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 64\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 39\n    }, this), token && role !== 'trainee' && /*#__PURE__*/_jsxDEV(Route, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(AdminRoutes, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 64\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 39\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n}\n_s(AppRouter, \"hfTeuykHMev6UMiB/hNK1PK1I3o=\", false, function () {\n  return [useNavigate];\n});\n_c = AppRouter;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(PermissionsProvider, {\n    children: /*#__PURE__*/_jsxDEV(NotificationProvider, {\n      children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        children: /*#__PURE__*/_jsxDEV(AppRouter, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppRouter\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Navigate", "useNavigate", "useEffect", "useRef", "AuthRoutes", "UserRoutes", "AdminRoutes", "PublicCourseDetails", "NotificationProvider", "PermissionsProvider", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AppRouter", "_s", "token", "localStorage", "getItem", "role", "navigate", "initialLoadRef", "current", "window", "location", "pathname", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON>", "Register", "ForgotPassword", "ForgotPasswordOTP", "RegisterOTP", "ResetPassword", "ActiveAccountPage", "Error401", "PageNotFound", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/App.js"], "sourcesContent": ["import { BrowserRouter, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\nimport { useEffect, useRef } from 'react';\n\nimport AuthRoutes from './routes/AuthRoutes.jsx';\nimport UserRoutes from './routes/UserRoutes.jsx';\nimport AdminRoutes from './routes/AdminRoutes.jsx';\nimport PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';\nimport { NotificationProvider } from './context/NotificationContext';\nimport { PermissionsProvider } from './context/PermissionsContext';\n\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\nimport '../src/assets/styles/custom.css';\nimport './App.css';\n\nfunction AppRouter() {\n  const token = localStorage.getItem('token');\n  const role = localStorage.getItem('role');\n  const navigate = useNavigate();\n  const initialLoadRef = useRef(true);\n  \n  useEffect(() => {\n    // Only redirect on initial load\n    if (initialLoadRef.current && token && window.location.pathname === '/') {\n      initialLoadRef.current = false;\n      if (role === 'trainee') {\n        navigate('/user/dashboard');\n      } else {\n        // For any other role (admin, trainer, etc.)\n        navigate('/admin/dashboard');\n      }\n    }\n  }, [token, role, navigate]);\n\n  return (\n    <Routes>\n      {/* Public routes - accessible without authentication */}\n      <Route path=\"/public/courseDetails/:encodedId\" element={<PublicCourseDetails />} />\n\n      {/* Auth routes - when not authenticated */}\n      {!token && (\n        <>\n          <Route path=\"/\" element={<Login />} />\n          <Route path=\"/auth/login\" element={<Login />} />\n          <Route path=\"/auth/register\" element={<Register />} />\n          <Route path=\"/auth/forgot-password\" element={<ForgotPassword />} />\n          <Route path=\"/auth/forgot-password-otp\" element={<ForgotPasswordOTP />} />\n          <Route path=\"/auth/register-otp\" element={<RegisterOTP />} />\n          <Route path=\"/auth/reset-password\" element={<ResetPassword />} />\n          <Route path=\"/auth/active-account\" element={<ActiveAccountPage />} />\n          <Route path=\"/error/401\" element={<Error401 />} />\n          <Route path=\"*\" element={<PageNotFound />} />\n        </>\n      )}\n\n      {/* User routes - when authenticated as trainee */}\n      {token && role === 'trainee' && <Route path=\"*\" element={<UserRoutes />} />}\n\n      {/* Admin routes - when authenticated as admin/trainer */}\n      {token && role !== 'trainee' && <Route path=\"*\" element={<AdminRoutes />} />}\n    </Routes>\n  );\n}\n\nfunction App() {\n  return (\n    <PermissionsProvider>\n    <NotificationProvider>\n      <BrowserRouter>\n        <AppRouter />\n      </BrowserRouter>\n    </NotificationProvider>\n    </PermissionsProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACtF,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEzC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,mBAAmB,MAAM,wCAAwC;AACxE,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,mBAAmB,QAAQ,8BAA8B;AAElE,OAAO,sCAAsC;AAC7C,OAAO,2CAA2C;AAClD,OAAO,iCAAiC;AACxC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,MAAMC,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EACzC,MAAME,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,cAAc,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACd;IACA,IAAImB,cAAc,CAACC,OAAO,IAAIN,KAAK,IAAIO,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,GAAG,EAAE;MACvEJ,cAAc,CAACC,OAAO,GAAG,KAAK;MAC9B,IAAIH,IAAI,KAAK,SAAS,EAAE;QACtBC,QAAQ,CAAC,iBAAiB,CAAC;MAC7B,CAAC,MAAM;QACL;QACAA,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;IACF;EACF,CAAC,EAAE,CAACJ,KAAK,EAAEG,IAAI,EAAEC,QAAQ,CAAC,CAAC;EAE3B,oBACET,OAAA,CAACb,MAAM;IAAA4B,QAAA,gBAELf,OAAA,CAACZ,KAAK;MAAC4B,IAAI,EAAC,kCAAkC;MAACC,OAAO,eAAEjB,OAAA,CAACJ,mBAAmB;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGlF,CAAChB,KAAK,iBACLL,OAAA,CAAAE,SAAA;MAAAa,QAAA,gBACEf,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEjB,OAAA,CAACsB,KAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtCrB,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,aAAa;QAACC,OAAO,eAAEjB,OAAA,CAACsB,KAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChDrB,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,gBAAgB;QAACC,OAAO,eAAEjB,OAAA,CAACuB,QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDrB,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,uBAAuB;QAACC,OAAO,eAAEjB,OAAA,CAACwB,cAAc;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnErB,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,2BAA2B;QAACC,OAAO,eAAEjB,OAAA,CAACyB,iBAAiB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1ErB,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,oBAAoB;QAACC,OAAO,eAAEjB,OAAA,CAAC0B,WAAW;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DrB,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,sBAAsB;QAACC,OAAO,eAAEjB,OAAA,CAAC2B,aAAa;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjErB,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,sBAAsB;QAACC,OAAO,eAAEjB,OAAA,CAAC4B,iBAAiB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrErB,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,YAAY;QAACC,OAAO,eAAEjB,OAAA,CAAC6B,QAAQ;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDrB,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEjB,OAAA,CAAC8B,YAAY;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA,eAC7C,CACH,EAGAhB,KAAK,IAAIG,IAAI,KAAK,SAAS,iBAAIR,OAAA,CAACZ,KAAK;MAAC4B,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEjB,OAAA,CAACN,UAAU;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAG1EhB,KAAK,IAAIG,IAAI,KAAK,SAAS,iBAAIR,OAAA,CAACZ,KAAK;MAAC4B,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEjB,OAAA,CAACL,WAAW;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtE,CAAC;AAEb;AAACjB,EAAA,CA/CQD,SAAS;EAAA,QAGCb,WAAW;AAAA;AAAAyC,EAAA,GAHrB5B,SAAS;AAiDlB,SAAS6B,GAAGA,CAAA,EAAG;EACb,oBACEhC,OAAA,CAACF,mBAAmB;IAAAiB,QAAA,eACpBf,OAAA,CAACH,oBAAoB;MAAAkB,QAAA,eACnBf,OAAA,CAACd,aAAa;QAAA6B,QAAA,eACZf,OAAA,CAACG,SAAS;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAE1B;AAACY,GAAA,GAVQD,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}