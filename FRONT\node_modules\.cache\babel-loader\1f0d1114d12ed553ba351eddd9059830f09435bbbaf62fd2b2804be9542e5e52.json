{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\auth\\\\Login.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useNavigate } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { validateEmail, validatePassword, validateForm } from '../../utils/validation';\nimport { getLogoByDomainAndAlt } from '../../utils/logoUtils';\nimport { loginApi, sendOTPApi, ActiveAccountOTPFromLoginPage } from '../../services/authService';\nimport { usePermissions } from '../../context/PermissionsContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Login() {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const navigate = useNavigate();\n  const {\n    setPermissionsFromAPI\n  } = usePermissions();\n\n  // Validation Rules\n  const validationRules = {\n    email: validateEmail,\n    password: validatePassword\n  };\n\n  // Handle Change\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Update both form data and clear errors in a single function\n    const newFormData = {\n      ...formData,\n      [name]: value\n    };\n    const newErrors = {\n      ...errors,\n      [name]: undefined\n    };\n    setFormData(newFormData);\n    setErrors(newErrors);\n  };\n\n  // Handle Submit\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const validation = validateForm(formData, validationRules);\n    if (validation.isValid) {\n      setIsLoading(true);\n      const payload = {\n        email: formData.email,\n        password: formData.password,\n        organization_url: window.location.origin\n      };\n      console.log(\"Payload being sent to API:-------------------------\", payload);\n      try {\n        var _response$data;\n        console.log(\"Payload being sent to API:\", payload);\n        const response = await loginApi(payload);\n        console.log(\"API response:\", response);\n\n        // ⛔ Email not verified - redirect to OTP page\n        if ((response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.user_details) === 0) {\n          console.log(\"Email not verified, sending OTP and redirecting to activation page\");\n          try {\n            // Call sendOTP API to send activation OTP\n            const otpPayload = {\n              email: formData.email,\n              domain: window.location.origin\n            };\n            await ActiveAccountOTPFromLoginPage(otpPayload);\n            console.log(\"OTP sent successfully for account activation\");\n            toast.success('Account registered but not activated. Please check your email.', {\n              position: \"top-center\",\n              autoClose: 2000,\n              hideProgressBar: false,\n              closeOnClick: true,\n              pauseOnHover: true,\n              draggable: true,\n              onClose: () => {\n                setIsLoading(false); // Turn off loader only when redirecting\n                navigate('/auth/active-account', {\n                  state: {\n                    email: formData.email\n                  }\n                });\n              }\n            });\n          } catch (otpError) {\n            console.error(\"Error sending OTP:\", otpError);\n            toast.error('Failed to send OTP. Please try again.', {\n              position: \"top-center\",\n              autoClose: 3000\n            });\n            setIsLoading(false);\n          }\n          return;\n        }\n\n        // Always wait 500ms before showing result\n        await new Promise(resolve => setTimeout(resolve, 500));\n        if (response.data.success) {\n          // Set permissions in context\n          setPermissionsFromAPI(response.data.data.permissions);\n          localStorage.setItem('token', response.data.data.access_token);\n          localStorage.setItem('refresh_token', response.data.data.refresh_token);\n          localStorage.setItem('user', JSON.stringify(response.data.data.user_details));\n          localStorage.setItem('organization_details', JSON.stringify(response.data.data.dashboard));\n          localStorage.setItem('role', response.data.data.role);\n          toast.success(response.data.data.message || 'Login successful', {\n            position: \"top-center\",\n            autoClose: 1000,\n            onClose: () => {\n              console.log(\"Role:\", response.data.data.role);\n              if (response.data.data.role === 'trainee') {\n                navigate('/user/dashboard');\n              } else {\n                navigate('/admin/dashboard');\n              }\n            }\n          });\n        } else {\n          toast.error(response.data.message || \"Login failed\", {\n            position: \"top-center\",\n            autoClose: 5000\n          });\n          setIsLoading(false);\n        }\n      } catch (error) {\n        var _error$response, _error$response2, _error$response2$data;\n        console.error(\"Login API error:\", error === null || error === void 0 ? void 0 : (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n        await new Promise(resolve => setTimeout(resolve, 500));\n        toast.error((error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Something went wrong\", {\n          position: \"top-center\",\n          autoClose: 5000\n        });\n        setIsLoading(false);\n      }\n    } else {\n      setErrors(validation.errors);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-logo\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: getLogoByDomainAndAlt().logo,\n        alt: getLogoByDomainAndAlt().alt,\n        style: {\n          width: '180px',\n          height: 'auto'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Login to LMS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Enter your email & password to login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"auth-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            children: \"Email Address\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"mdi:email-outline\",\n              className: \"auth-input-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleChange,\n              placeholder: \"<EMAIL>\",\n              className: `auth-input ${errors.email ? 'error' : ''}`,\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"auth-error-message\",\n            children: errors.email[0]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 30\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"mdi:lock-outline\",\n              className: \"auth-input-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: showPassword ? \"text\" : \"password\",\n              id: \"password\",\n              name: \"password\",\n              value: formData.password,\n              onChange: handleChange,\n              placeholder: \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\",\n              className: `auth-input ${errors.password ? 'error' : ''}`,\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"auth-password-toggle\",\n              onClick: () => setShowPassword(!showPassword),\n              \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n              disabled: isLoading,\n              children: /*#__PURE__*/_jsxDEV(Icon, {\n                icon: showPassword ? \"mdi:eye-off-outline\" : \"mdi:eye-outline\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"auth-error-message\",\n            children: errors.password[0]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-forgot-password\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/auth/forgot-password\",\n            className: isLoading ? 'disabled-link' : '',\n            children: \"Forgot Password?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: `btn btn-primary ${isLoading ? 'loading' : ''}`,\n          disabled: isLoading,\n          children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this), \"Logging in...\"]\n          }, void 0, true) : 'Login'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-options\",\n          children: [\"Don't have an account? \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/auth/register\",\n            className: isLoading ? 'disabled-link' : '',\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 36\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 157,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"CZ9m6e0BHqLCXCPRtXrw23HXOY8=\", false, function () {\n  return [useNavigate, usePermissions];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Icon", "useNavigate", "toast", "validateEmail", "validatePassword", "validateForm", "getLogoByDomainAndAlt", "loginApi", "sendOTPApi", "ActiveAccountOTPFromLoginPage", "usePermissions", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "formData", "setFormData", "email", "password", "errors", "setErrors", "showPassword", "setShowPassword", "isLoading", "setIsLoading", "navigate", "setPermissionsFromAPI", "validationRules", "handleChange", "e", "name", "value", "target", "newFormData", "newErrors", "undefined", "handleSubmit", "preventDefault", "validation", "<PERSON><PERSON><PERSON><PERSON>", "payload", "organization_url", "window", "location", "origin", "console", "log", "_response$data", "response", "data", "user_details", "otpPayload", "domain", "success", "position", "autoClose", "hideProgressBar", "closeOnClick", "pauseOnHover", "draggable", "onClose", "state", "otpError", "error", "Promise", "resolve", "setTimeout", "permissions", "localStorage", "setItem", "access_token", "refresh_token", "JSON", "stringify", "dashboard", "role", "message", "_error$response", "_error$response2", "_error$response2$data", "className", "children", "src", "logo", "alt", "style", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "icon", "type", "id", "onChange", "placeholder", "disabled", "onClick", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/auth/Login.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useNavigate } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { validateEmail, validatePassword, validateForm } from '../../utils/validation';\nimport { getLogoByDomainAndAlt } from '../../utils/logoUtils';\nimport { loginApi, sendOTPApi, ActiveAccountOTPFromLoginPage } from '../../services/authService';\nimport { usePermissions } from '../../context/PermissionsContext';\n\n\nfunction Login() {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const navigate = useNavigate();\n  const { setPermissionsFromAPI } = usePermissions();\n\n  // Validation Rules\n  const validationRules = {\n    email: validateEmail,\n    password: validatePassword\n  };\n\n  // Handle Change\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    \n    // Update both form data and clear errors in a single function\n    const newFormData = { ...formData, [name]: value };\n    const newErrors = { ...errors, [name]: undefined };\n    \n    setFormData(newFormData);\n    setErrors(newErrors);\n  };\n\n  // Handle Submit\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n  \n    const validation = validateForm(formData, validationRules);\n    if (validation.isValid) {\n      setIsLoading(true);\n  \n      const payload = {\n        email: formData.email,\n        password: formData.password,\n        organization_url: window.location.origin\n      };\n  \n      console.log(\"Payload being sent to API:-------------------------\", payload);\n\n      try {\n        console.log(\"Payload being sent to API:\", payload);\n  \n        const response = await loginApi(payload);\n        console.log(\"API response:\", response);\n  \n        // ⛔ Email not verified - redirect to OTP page\n        if (response?.data?.user_details === 0) {\n          console.log(\"Email not verified, sending OTP and redirecting to activation page\");\n\n          try {\n            // Call sendOTP API to send activation OTP\n            const otpPayload = {\n              email: formData.email,\n              domain: window.location.origin\n            };\n\n            await ActiveAccountOTPFromLoginPage(otpPayload);\n            console.log(\"OTP sent successfully for account activation\");\n\n           toast.success('Account registered but not activated. Please check your email.', {\n              position: \"top-center\",\n              autoClose: 2000,\n              hideProgressBar: false,\n              closeOnClick: true,\n              pauseOnHover: true,\n              draggable: true,\n              onClose: () => {\n                setIsLoading(false); // Turn off loader only when redirecting\n                navigate('/auth/active-account', {\n                  state: {\n                    email: formData.email\n                  }\n                });\n              }\n            });\n\n            \n          } catch (otpError) {\n            console.error(\"Error sending OTP:\", otpError);\n            toast.error('Failed to send OTP. Please try again.', {\n              position: \"top-center\",\n              autoClose: 3000\n            });\n            setIsLoading(false);\n          }\n          return;\n        }\n  \n        // Always wait 500ms before showing result\n        await new Promise(resolve => setTimeout(resolve, 500));\n  \n        if (response.data.success) {\n          // Set permissions in context\n          setPermissionsFromAPI(response.data.data.permissions);\n  \n          localStorage.setItem('token', response.data.data.access_token);\n          localStorage.setItem('refresh_token', response.data.data.refresh_token);\n          localStorage.setItem('user', JSON.stringify(response.data.data.user_details));\n          localStorage.setItem('organization_details', JSON.stringify(response.data.data.dashboard));\n          localStorage.setItem('role', response.data.data.role);\n  \n          toast.success(response.data.data.message || 'Login successful', {\n            position: \"top-center\",\n            autoClose: 1000,\n            onClose: () => {\n              console.log(\"Role:\", response.data.data.role);\n              if (response.data.data.role === 'trainee') {\n                navigate('/user/dashboard');\n              } else {\n                navigate('/admin/dashboard');\n              }\n            }\n          });\n        } else {\n          toast.error(response.data.message || \"Login failed\", {\n            position: \"top-center\",\n            autoClose: 5000\n          });\n          setIsLoading(false);\n        }\n  \n      } catch (error) {\n        console.error(\"Login API error:\", error?.response?.data);\n  \n        await new Promise(resolve => setTimeout(resolve, 500));\n  \n        toast.error(error?.response?.data?.message || \"Something went wrong\", {\n          position: \"top-center\",\n          autoClose: 5000\n        });\n        setIsLoading(false);\n      }\n    } else {\n      setErrors(validation.errors);\n    }\n  };\n  \n  \n\n  return (\n    <div className=\"auth-container\">\n      {/* Logo  */}\n      <div className=\"auth-logo\">\n        <img src={getLogoByDomainAndAlt().logo} alt={getLogoByDomainAndAlt().alt} style={{ width: '180px', height: 'auto' }} />\n      </div>\n      {/* Login Card */}\n      <div className=\"auth-card\">\n        <div className=\"auth-header\">\n          <h2>Login to LMS</h2>\n          <p>Enter your email & password to login</p>\n        </div>\n        \n        <form onSubmit={handleSubmit} className=\"auth-form\">\n          <div className=\"auth-form-group\">\n            <label htmlFor=\"email\">Email Address</label>\n            <div className=\"auth-input-wrapper\">\n              <Icon icon=\"mdi:email-outline\" className=\"auth-input-icon\" />\n              <input \n                type=\"text\"\n                id=\"email\" \n                name=\"email\"\n                value={formData.email}\n                onChange={handleChange}\n                placeholder=\"<EMAIL>\" \n                className={`auth-input ${errors.email ? 'error' : ''}`}\n                disabled={isLoading}\n              />\n            </div>\n            {errors.email && <span className=\"auth-error-message\">{errors.email[0]}</span>}\n          </div>\n          \n          <div className=\"auth-form-group\">\n            <label htmlFor=\"password\">Password</label>\n            <div className=\"auth-input-wrapper\">\n              <Icon icon=\"mdi:lock-outline\" className=\"auth-input-icon\" />\n              <input \n                type={showPassword ? \"text\" : \"password\"} \n                id=\"password\" \n                name=\"password\"\n                value={formData.password}\n                onChange={handleChange}\n                placeholder=\"••••••\" \n                className={`auth-input ${errors.password ? 'error' : ''}`}\n                disabled={isLoading}\n              />\n              <button \n                type=\"button\" \n                className=\"auth-password-toggle\" \n                onClick={() => setShowPassword(!showPassword)}\n                aria-label={showPassword ? \"Hide password\" : \"Show password\"}\n                disabled={isLoading}\n              >\n                <Icon icon={showPassword ? \"mdi:eye-off-outline\" : \"mdi:eye-outline\"} />\n              </button>\n            </div>\n            {errors.password && <span className=\"auth-error-message\">{errors.password[0]}</span>}\n          </div>\n          \n          <div className=\"auth-forgot-password\">\n            <a href=\"/auth/forgot-password\" className={isLoading ? 'disabled-link' : ''}>Forgot Password?</a>\n          </div>\n          \n          <button \n            type=\"submit\" \n            className={`btn btn-primary ${isLoading ? 'loading' : ''}`}\n            disabled={isLoading}\n          >\n            {isLoading ? (\n              <>\n                <div className=\"spinner\"></div>\n                Logging in...\n              </>\n            ) : (\n              'Login'\n            )}\n          </button>\n          \n          <div className=\"auth-options\">\n            Don't have an account? <a href=\"/auth/register\" className={isLoading ? 'disabled-link' : ''}>Create Account</a>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,wBAAwB;AACtF,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,QAAQ,EAAEC,UAAU,EAAEC,6BAA6B,QAAQ,4BAA4B;AAChG,SAASC,cAAc,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGlE,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IACvCoB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM4B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2B;EAAsB,CAAC,GAAGlB,cAAc,CAAC,CAAC;;EAElD;EACA,MAAMmB,eAAe,GAAG;IACtBV,KAAK,EAAEhB,aAAa;IACpBiB,QAAQ,EAAEhB;EACZ,CAAC;;EAED;EACA,MAAM0B,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA,MAAMC,WAAW,GAAG;MAAE,GAAGlB,QAAQ;MAAE,CAACe,IAAI,GAAGC;IAAM,CAAC;IAClD,MAAMG,SAAS,GAAG;MAAE,GAAGf,MAAM;MAAE,CAACW,IAAI,GAAGK;IAAU,CAAC;IAElDnB,WAAW,CAACiB,WAAW,CAAC;IACxBb,SAAS,CAACc,SAAS,CAAC;EACtB,CAAC;;EAED;EACA,MAAME,YAAY,GAAG,MAAOP,CAAC,IAAK;IAChCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAElB,MAAMC,UAAU,GAAGnC,YAAY,CAACY,QAAQ,EAAEY,eAAe,CAAC;IAC1D,IAAIW,UAAU,CAACC,OAAO,EAAE;MACtBf,YAAY,CAAC,IAAI,CAAC;MAElB,MAAMgB,OAAO,GAAG;QACdvB,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAC3BuB,gBAAgB,EAAEC,MAAM,CAACC,QAAQ,CAACC;MACpC,CAAC;MAEDC,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEN,OAAO,CAAC;MAE3E,IAAI;QAAA,IAAAO,cAAA;QACFF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEN,OAAO,CAAC;QAElD,MAAMQ,QAAQ,GAAG,MAAM3C,QAAQ,CAACmC,OAAO,CAAC;QACxCK,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEE,QAAQ,CAAC;;QAEtC;QACA,IAAI,CAAAA,QAAQ,aAARA,QAAQ,wBAAAD,cAAA,GAARC,QAAQ,CAAEC,IAAI,cAAAF,cAAA,uBAAdA,cAAA,CAAgBG,YAAY,MAAK,CAAC,EAAE;UACtCL,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;UAEjF,IAAI;YACF;YACA,MAAMK,UAAU,GAAG;cACjBlC,KAAK,EAAEF,QAAQ,CAACE,KAAK;cACrBmC,MAAM,EAAEV,MAAM,CAACC,QAAQ,CAACC;YAC1B,CAAC;YAED,MAAMrC,6BAA6B,CAAC4C,UAAU,CAAC;YAC/CN,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;YAE5D9C,KAAK,CAACqD,OAAO,CAAC,gEAAgE,EAAE;cAC7EC,QAAQ,EAAE,YAAY;cACtBC,SAAS,EAAE,IAAI;cACfC,eAAe,EAAE,KAAK;cACtBC,YAAY,EAAE,IAAI;cAClBC,YAAY,EAAE,IAAI;cAClBC,SAAS,EAAE,IAAI;cACfC,OAAO,EAAEA,CAAA,KAAM;gBACbpC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;gBACrBC,QAAQ,CAAC,sBAAsB,EAAE;kBAC/BoC,KAAK,EAAE;oBACL5C,KAAK,EAAEF,QAAQ,CAACE;kBAClB;gBACF,CAAC,CAAC;cACJ;YACF,CAAC,CAAC;UAGJ,CAAC,CAAC,OAAO6C,QAAQ,EAAE;YACjBjB,OAAO,CAACkB,KAAK,CAAC,oBAAoB,EAAED,QAAQ,CAAC;YAC7C9D,KAAK,CAAC+D,KAAK,CAAC,uCAAuC,EAAE;cACnDT,QAAQ,EAAE,YAAY;cACtBC,SAAS,EAAE;YACb,CAAC,CAAC;YACF/B,YAAY,CAAC,KAAK,CAAC;UACrB;UACA;QACF;;QAEA;QACA,MAAM,IAAIwC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;QAEtD,IAAIjB,QAAQ,CAACC,IAAI,CAACI,OAAO,EAAE;UACzB;UACA3B,qBAAqB,CAACsB,QAAQ,CAACC,IAAI,CAACA,IAAI,CAACkB,WAAW,CAAC;UAErDC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAErB,QAAQ,CAACC,IAAI,CAACA,IAAI,CAACqB,YAAY,CAAC;UAC9DF,YAAY,CAACC,OAAO,CAAC,eAAe,EAAErB,QAAQ,CAACC,IAAI,CAACA,IAAI,CAACsB,aAAa,CAAC;UACvEH,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEG,IAAI,CAACC,SAAS,CAACzB,QAAQ,CAACC,IAAI,CAACA,IAAI,CAACC,YAAY,CAAC,CAAC;UAC7EkB,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAEG,IAAI,CAACC,SAAS,CAACzB,QAAQ,CAACC,IAAI,CAACA,IAAI,CAACyB,SAAS,CAAC,CAAC;UAC1FN,YAAY,CAACC,OAAO,CAAC,MAAM,EAAErB,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC0B,IAAI,CAAC;UAErD3E,KAAK,CAACqD,OAAO,CAACL,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC2B,OAAO,IAAI,kBAAkB,EAAE;YAC9DtB,QAAQ,EAAE,YAAY;YACtBC,SAAS,EAAE,IAAI;YACfK,OAAO,EAAEA,CAAA,KAAM;cACbf,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEE,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC0B,IAAI,CAAC;cAC7C,IAAI3B,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC0B,IAAI,KAAK,SAAS,EAAE;gBACzClD,QAAQ,CAAC,iBAAiB,CAAC;cAC7B,CAAC,MAAM;gBACLA,QAAQ,CAAC,kBAAkB,CAAC;cAC9B;YACF;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACLzB,KAAK,CAAC+D,KAAK,CAACf,QAAQ,CAACC,IAAI,CAAC2B,OAAO,IAAI,cAAc,EAAE;YACnDtB,QAAQ,EAAE,YAAY;YACtBC,SAAS,EAAE;UACb,CAAC,CAAC;UACF/B,YAAY,CAAC,KAAK,CAAC;QACrB;MAEF,CAAC,CAAC,OAAOuC,KAAK,EAAE;QAAA,IAAAc,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;QACdlC,OAAO,CAACkB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,aAALA,KAAK,wBAAAc,eAAA,GAALd,KAAK,CAAEf,QAAQ,cAAA6B,eAAA,uBAAfA,eAAA,CAAiB5B,IAAI,CAAC;QAExD,MAAM,IAAIe,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;QAEtDjE,KAAK,CAAC+D,KAAK,CAAC,CAAAA,KAAK,aAALA,KAAK,wBAAAe,gBAAA,GAALf,KAAK,CAAEf,QAAQ,cAAA8B,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiB7B,IAAI,cAAA8B,qBAAA,uBAArBA,qBAAA,CAAuBH,OAAO,KAAI,sBAAsB,EAAE;UACpEtB,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAE;QACb,CAAC,CAAC;QACF/B,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC,MAAM;MACLJ,SAAS,CAACkB,UAAU,CAACnB,MAAM,CAAC;IAC9B;EACF,CAAC;EAID,oBACET,OAAA;IAAKsE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAE7BvE,OAAA;MAAKsE,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBvE,OAAA;QAAKwE,GAAG,EAAE9E,qBAAqB,CAAC,CAAC,CAAC+E,IAAK;QAACC,GAAG,EAAEhF,qBAAqB,CAAC,CAAC,CAACgF,GAAI;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpH,CAAC,eAENjF,OAAA;MAAKsE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBvE,OAAA;QAAKsE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvE,OAAA;UAAAuE,QAAA,EAAI;QAAY;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBjF,OAAA;UAAAuE,QAAA,EAAG;QAAoC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eAENjF,OAAA;QAAMkF,QAAQ,EAAExD,YAAa;QAAC4C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACjDvE,OAAA;UAAKsE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BvE,OAAA;YAAOmF,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAAa;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CjF,OAAA;YAAKsE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCvE,OAAA,CAACZ,IAAI;cAACgG,IAAI,EAAC,mBAAmB;cAACd,SAAS,EAAC;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7DjF,OAAA;cACEqF,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,OAAO;cACVlE,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEhB,QAAQ,CAACE,KAAM;cACtBgF,QAAQ,EAAErE,YAAa;cACvBsE,WAAW,EAAC,mBAAmB;cAC/BlB,SAAS,EAAE,cAAc7D,MAAM,CAACF,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;cACvDkF,QAAQ,EAAE5E;YAAU;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACLxE,MAAM,CAACF,KAAK,iBAAIP,OAAA;YAAMsE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAE9D,MAAM,CAACF,KAAK,CAAC,CAAC;UAAC;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eAENjF,OAAA;UAAKsE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BvE,OAAA;YAAOmF,OAAO,EAAC,UAAU;YAAAZ,QAAA,EAAC;UAAQ;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1CjF,OAAA;YAAKsE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCvE,OAAA,CAACZ,IAAI;cAACgG,IAAI,EAAC,kBAAkB;cAACd,SAAS,EAAC;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DjF,OAAA;cACEqF,IAAI,EAAE1E,YAAY,GAAG,MAAM,GAAG,UAAW;cACzC2E,EAAE,EAAC,UAAU;cACblE,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEhB,QAAQ,CAACG,QAAS;cACzB+E,QAAQ,EAAErE,YAAa;cACvBsE,WAAW,EAAC,sCAAQ;cACpBlB,SAAS,EAAE,cAAc7D,MAAM,CAACD,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;cAC1DiF,QAAQ,EAAE5E;YAAU;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACFjF,OAAA;cACEqF,IAAI,EAAC,QAAQ;cACbf,SAAS,EAAC,sBAAsB;cAChCoB,OAAO,EAAEA,CAAA,KAAM9E,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9C,cAAYA,YAAY,GAAG,eAAe,GAAG,eAAgB;cAC7D8E,QAAQ,EAAE5E,SAAU;cAAA0D,QAAA,eAEpBvE,OAAA,CAACZ,IAAI;gBAACgG,IAAI,EAAEzE,YAAY,GAAG,qBAAqB,GAAG;cAAkB;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACLxE,MAAM,CAACD,QAAQ,iBAAIR,OAAA;YAAMsE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAE9D,MAAM,CAACD,QAAQ,CAAC,CAAC;UAAC;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eAENjF,OAAA;UAAKsE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnCvE,OAAA;YAAG2F,IAAI,EAAC,uBAAuB;YAACrB,SAAS,EAAEzD,SAAS,GAAG,eAAe,GAAG,EAAG;YAAA0D,QAAA,EAAC;UAAgB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CAAC,eAENjF,OAAA;UACEqF,IAAI,EAAC,QAAQ;UACbf,SAAS,EAAE,mBAAmBzD,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;UAC3D4E,QAAQ,EAAE5E,SAAU;UAAA0D,QAAA,EAEnB1D,SAAS,gBACRb,OAAA,CAAAE,SAAA;YAAAqE,QAAA,gBACEvE,OAAA;cAAKsE,SAAS,EAAC;YAAS;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,iBAEjC;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAETjF,OAAA;UAAKsE,SAAS,EAAC,cAAc;UAAAC,QAAA,GAAC,yBACL,eAAAvE,OAAA;YAAG2F,IAAI,EAAC,gBAAgB;YAACrB,SAAS,EAAEzD,SAAS,GAAG,eAAe,GAAG,EAAG;YAAA0D,QAAA,EAAC;UAAc;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC7E,EAAA,CAtOQD,KAAK;EAAA,QAQKd,WAAW,EACMS,cAAc;AAAA;AAAA8F,EAAA,GATzCzF,KAAK;AAwOd,eAAeA,KAAK;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}