{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\nimport { useEffect, useRef } from 'react';\nimport AuthRoutes from './routes/AuthRoutes.jsx';\nimport UserRoutes from './routes/UserRoutes.jsx';\nimport AdminRoutes from './routes/AdminRoutes.jsx';\nimport PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';\nimport { NotificationProvider } from './context/NotificationContext';\nimport { PermissionsProvider } from './context/PermissionsContext';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\nimport '../src/assets/styles/custom.css';\nimport './App.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppRouter() {\n  _s();\n  const token = localStorage.getItem('token');\n  const role = localStorage.getItem('role');\n  const navigate = useNavigate();\n  const initialLoadRef = useRef(true);\n  useEffect(() => {\n    // Only redirect on initial load\n    if (initialLoadRef.current && token && window.location.pathname === '/') {\n      initialLoadRef.current = false;\n      if (role === 'trainee') {\n        navigate('/user/dashboard');\n      } else {\n        // For any other role (admin, trainer, etc.)\n        navigate('/admin/dashboard');\n      }\n    }\n  }, [token, role, navigate]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [!token && /*#__PURE__*/_jsxDEV(AuthRoutes, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 18\n    }, this), token && role === 'trainee' && /*#__PURE__*/_jsxDEV(UserRoutes, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 39\n    }, this), token && role !== 'trainee' && /*#__PURE__*/_jsxDEV(AdminRoutes, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 39\n    }, this)]\n  }, void 0, true);\n}\n_s(AppRouter, \"hfTeuykHMev6UMiB/hNK1PK1I3o=\", false, function () {\n  return [useNavigate];\n});\n_c = AppRouter;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(PermissionsProvider, {\n    children: /*#__PURE__*/_jsxDEV(NotificationProvider, {\n      children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        children: /*#__PURE__*/_jsxDEV(AppRouter, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppRouter\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Navigate", "useNavigate", "useEffect", "useRef", "AuthRoutes", "UserRoutes", "AdminRoutes", "PublicCourseDetails", "NotificationProvider", "PermissionsProvider", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AppRouter", "_s", "token", "localStorage", "getItem", "role", "navigate", "initialLoadRef", "current", "window", "location", "pathname", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/App.js"], "sourcesContent": ["import { BrowserRouter, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\r\nimport { useEffect, useRef } from 'react';\r\n\r\nimport AuthRoutes from './routes/AuthRoutes.jsx';\r\nimport UserRoutes from './routes/UserRoutes.jsx';\r\nimport AdminRoutes from './routes/AdminRoutes.jsx';\r\nimport PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';\r\nimport { NotificationProvider } from './context/NotificationContext';\r\nimport { PermissionsProvider } from './context/PermissionsContext';\r\n\r\nimport 'bootstrap/dist/css/bootstrap.min.css';\r\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\r\nimport '../src/assets/styles/custom.css';\r\nimport './App.css';\r\n\r\nfunction AppRouter() {\r\n  const token = localStorage.getItem('token');\r\n  const role = localStorage.getItem('role');\r\n  const navigate = useNavigate();\r\n  const initialLoadRef = useRef(true);\r\n  \r\n  useEffect(() => {\r\n    // Only redirect on initial load\r\n    if (initialLoadRef.current && token && window.location.pathname === '/') {\r\n      initialLoadRef.current = false;\r\n      if (role === 'trainee') {\r\n        navigate('/user/dashboard');\r\n      } else {\r\n        // For any other role (admin, trainer, etc.)\r\n        navigate('/admin/dashboard');\r\n      }\r\n    }\r\n  }, [token, role, navigate]);\r\n\r\n  return (\r\n    <>\r\n      {/* Public routes - accessible without authentication */}\r\n   \r\n      \r\n      {/* Protected routes based on authentication */}\r\n      {!token && <AuthRoutes />}\r\n      {token && role === 'trainee' && <UserRoutes />}\r\n      {token && role !== 'trainee' && <AdminRoutes />}\r\n    </>\r\n  );\r\n}\r\n\r\nfunction App() {\r\n  return (\r\n    <PermissionsProvider>\r\n    <NotificationProvider>\r\n      <BrowserRouter>\r\n        <AppRouter />\r\n      </BrowserRouter>\r\n    </NotificationProvider>\r\n    </PermissionsProvider>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,SAASA,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACtF,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEzC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,mBAAmB,MAAM,wCAAwC;AACxE,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,mBAAmB,QAAQ,8BAA8B;AAElE,OAAO,sCAAsC;AAC7C,OAAO,2CAA2C;AAClD,OAAO,iCAAiC;AACxC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,MAAMC,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EACzC,MAAME,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,cAAc,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACd;IACA,IAAImB,cAAc,CAACC,OAAO,IAAIN,KAAK,IAAIO,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,GAAG,EAAE;MACvEJ,cAAc,CAACC,OAAO,GAAG,KAAK;MAC9B,IAAIH,IAAI,KAAK,SAAS,EAAE;QACtBC,QAAQ,CAAC,iBAAiB,CAAC;MAC7B,CAAC,MAAM;QACL;QACAA,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;IACF;EACF,CAAC,EAAE,CAACJ,KAAK,EAAEG,IAAI,EAAEC,QAAQ,CAAC,CAAC;EAE3B,oBACET,OAAA,CAAAE,SAAA;IAAAa,QAAA,GAKG,CAACV,KAAK,iBAAIL,OAAA,CAACP,UAAU;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACxBd,KAAK,IAAIG,IAAI,KAAK,SAAS,iBAAIR,OAAA,CAACN,UAAU;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC7Cd,KAAK,IAAIG,IAAI,KAAK,SAAS,iBAAIR,OAAA,CAACL,WAAW;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eAC/C,CAAC;AAEP;AAACf,EAAA,CA9BQD,SAAS;EAAA,QAGCb,WAAW;AAAA;AAAA8B,EAAA,GAHrBjB,SAAS;AAgClB,SAASkB,GAAGA,CAAA,EAAG;EACb,oBACErB,OAAA,CAACF,mBAAmB;IAAAiB,QAAA,eACpBf,OAAA,CAACH,oBAAoB;MAAAkB,QAAA,eACnBf,OAAA,CAACd,aAAa;QAAA6B,QAAA,eACZf,OAAA,CAACG,SAAS;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAE1B;AAACG,GAAA,GAVQD,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}