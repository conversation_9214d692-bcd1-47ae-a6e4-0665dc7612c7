{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\nimport { useEffect, useRef } from 'react';\nimport UserRoutes from './routes/UserRoutes.jsx';\nimport AdminRoutes from './routes/AdminRoutes.jsx';\nimport PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';\nimport { NotificationProvider } from './context/NotificationContext';\nimport { PermissionsProvider } from './context/PermissionsContext';\nimport { ToastContainer } from 'react-toastify';\n\n// Import auth pages\nimport Login from './pages/auth/Login';\nimport Register from './pages/auth/Register';\nimport RegisterOTP from './pages/auth/RegisterOTP';\nimport ForgotPassword from './pages/auth/ForgotPassword';\nimport ForgotPasswordOTP from './pages/auth/ForgotPasswordOTP';\nimport ResetPassword from './pages/auth/ResetPassword';\nimport ActiveAccountPage from './pages/auth/ActiveAccountPage';\nimport Error401 from './pages/error/Error401';\nimport PageNotFound from './pages/error/PageNotFound';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\nimport '../src/assets/styles/custom.css';\nimport './App.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppRouter() {\n  _s();\n  const token = localStorage.getItem('token');\n  const role = localStorage.getItem('role');\n  const navigate = useNavigate();\n  const initialLoadRef = useRef(true);\n  useEffect(() => {\n    // Only redirect on initial load\n    if (initialLoadRef.current && token && window.location.pathname === '/') {\n      initialLoadRef.current = false;\n      if (role === 'trainee') {\n        navigate('/user/dashboard');\n      } else {\n        // For any other role (admin, trainer, etc.)\n        navigate('/admin/dashboard');\n      }\n    }\n  }, [token, role, navigate]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/public/courseDetails/:encodedId\",\n        element: /*#__PURE__*/_jsxDEV(PublicCourseDetails, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 65\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), !token && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 48\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/register\",\n          element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 51\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/forgot-password\",\n          element: /*#__PURE__*/_jsxDEV(ForgotPassword, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 58\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/forgot-password-otp\",\n          element: /*#__PURE__*/_jsxDEV(ForgotPasswordOTP, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 62\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/register-otp\",\n          element: /*#__PURE__*/_jsxDEV(RegisterOTP, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/reset-password\",\n          element: /*#__PURE__*/_jsxDEV(ResetPassword, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 57\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/active-account\",\n          element: /*#__PURE__*/_jsxDEV(ActiveAccountPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 57\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/error/401\",\n          element: /*#__PURE__*/_jsxDEV(Error401, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(PageNotFound, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), token && role === 'trainee' && /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(UserRoutes, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 66\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 41\n      }, this), token && role !== 'trainee' && /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(AdminRoutes, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 66\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 41\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(AppRouter, \"hfTeuykHMev6UMiB/hNK1PK1I3o=\", false, function () {\n  return [useNavigate];\n});\n_c = AppRouter;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(PermissionsProvider, {\n    children: /*#__PURE__*/_jsxDEV(NotificationProvider, {\n      children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        children: /*#__PURE__*/_jsxDEV(AppRouter, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppRouter\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Navigate", "useNavigate", "useEffect", "useRef", "UserRoutes", "AdminRoutes", "PublicCourseDetails", "NotificationProvider", "PermissionsProvider", "ToastContainer", "<PERSON><PERSON>", "Register", "RegisterOTP", "ForgotPassword", "ForgotPasswordOTP", "ResetPassword", "ActiveAccountPage", "Error401", "PageNotFound", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AppRouter", "_s", "token", "localStorage", "getItem", "role", "navigate", "initialLoadRef", "current", "window", "location", "pathname", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/App.js"], "sourcesContent": ["import { BrowserRouter, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\r\nimport { useEffect, useRef } from 'react';\r\n\r\nimport UserRoutes from './routes/UserRoutes.jsx';\r\nimport AdminRoutes from './routes/AdminRoutes.jsx';\r\nimport PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';\r\nimport { NotificationProvider } from './context/NotificationContext';\r\nimport { PermissionsProvider } from './context/PermissionsContext';\r\nimport { ToastContainer } from 'react-toastify';\r\n\r\n// Import auth pages\r\nimport Login from './pages/auth/Login';\r\nimport Register from './pages/auth/Register';\r\nimport RegisterOTP from './pages/auth/RegisterOTP';\r\nimport ForgotPassword from './pages/auth/ForgotPassword';\r\nimport ForgotPasswordOTP from './pages/auth/ForgotPasswordOTP';\r\nimport ResetPassword from './pages/auth/ResetPassword';\r\nimport ActiveAccountPage from './pages/auth/ActiveAccountPage';\r\nimport Error401 from './pages/error/Error401';\r\nimport PageNotFound from './pages/error/PageNotFound';\r\n\r\nimport 'bootstrap/dist/css/bootstrap.min.css';\r\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\r\nimport '../src/assets/styles/custom.css';\r\nimport './App.css';\r\n\r\nfunction AppRouter() {\r\n  const token = localStorage.getItem('token');\r\n  const role = localStorage.getItem('role');\r\n  const navigate = useNavigate();\r\n  const initialLoadRef = useRef(true);\r\n  \r\n  useEffect(() => {\r\n    // Only redirect on initial load\r\n    if (initialLoadRef.current && token && window.location.pathname === '/') {\r\n      initialLoadRef.current = false;\r\n      if (role === 'trainee') {\r\n        navigate('/user/dashboard');\r\n      } else {\r\n        // For any other role (admin, trainer, etc.)\r\n        navigate('/admin/dashboard');\r\n      }\r\n    }\r\n  }, [token, role, navigate]);\r\n\r\n  return (\r\n    <>\r\n      <Routes>\r\n        {/* Public routes - accessible without authentication */}\r\n        <Route path=\"/public/courseDetails/:encodedId\" element={<PublicCourseDetails />} />\r\n\r\n        {/* Auth routes - when not authenticated */}\r\n        {!token && (\r\n          <>\r\n            <Route path=\"/\" element={<Login />} />\r\n            <Route path=\"/auth/login\" element={<Login />} />\r\n            <Route path=\"/auth/register\" element={<Register />} />\r\n            <Route path=\"/auth/forgot-password\" element={<ForgotPassword />} />\r\n            <Route path=\"/auth/forgot-password-otp\" element={<ForgotPasswordOTP />} />\r\n            <Route path=\"/auth/register-otp\" element={<RegisterOTP />} />\r\n            <Route path=\"/auth/reset-password\" element={<ResetPassword />} />\r\n            <Route path=\"/auth/active-account\" element={<ActiveAccountPage />} />\r\n            <Route path=\"/error/401\" element={<Error401 />} />\r\n            <Route path=\"*\" element={<PageNotFound />} />\r\n          </>\r\n        )}\r\n\r\n        {/* User routes - when authenticated as trainee */}\r\n        {token && role === 'trainee' && <Route path=\"*\" element={<UserRoutes />} />}\r\n\r\n        {/* Admin routes - when authenticated as admin/trainer */}\r\n        {token && role !== 'trainee' && <Route path=\"*\" element={<AdminRoutes />} />}\r\n      </Routes>\r\n      <ToastContainer />\r\n    </>\r\n  );\r\n}\r\n\r\nfunction App() {\r\n  return (\r\n    <PermissionsProvider>\r\n    <NotificationProvider>\r\n      <BrowserRouter>\r\n        <AppRouter />\r\n      </BrowserRouter>\r\n    </NotificationProvider>\r\n    </PermissionsProvider>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,SAASA,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACtF,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEzC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,mBAAmB,MAAM,wCAAwC;AACxE,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,cAAc,QAAQ,gBAAgB;;AAE/C;AACA,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,YAAY,MAAM,4BAA4B;AAErD,OAAO,sCAAsC;AAC7C,OAAO,2CAA2C;AAClD,OAAO,iCAAiC;AACxC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,MAAMC,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EACzC,MAAME,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,cAAc,GAAG3B,MAAM,CAAC,IAAI,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACd;IACA,IAAI4B,cAAc,CAACC,OAAO,IAAIN,KAAK,IAAIO,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,GAAG,EAAE;MACvEJ,cAAc,CAACC,OAAO,GAAG,KAAK;MAC9B,IAAIH,IAAI,KAAK,SAAS,EAAE;QACtBC,QAAQ,CAAC,iBAAiB,CAAC;MAC7B,CAAC,MAAM;QACL;QACAA,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;IACF;EACF,CAAC,EAAE,CAACJ,KAAK,EAAEG,IAAI,EAAEC,QAAQ,CAAC,CAAC;EAE3B,oBACET,OAAA,CAAAE,SAAA;IAAAa,QAAA,gBACEf,OAAA,CAACtB,MAAM;MAAAqC,QAAA,gBAELf,OAAA,CAACrB,KAAK;QAACqC,IAAI,EAAC,kCAAkC;QAACC,OAAO,eAAEjB,OAAA,CAACd,mBAAmB;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGlF,CAAChB,KAAK,iBACLL,OAAA,CAAAE,SAAA;QAAAa,QAAA,gBACEf,OAAA,CAACrB,KAAK;UAACqC,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEjB,OAAA,CAACV,KAAK;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtCrB,OAAA,CAACrB,KAAK;UAACqC,IAAI,EAAC,aAAa;UAACC,OAAO,eAAEjB,OAAA,CAACV,KAAK;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDrB,OAAA,CAACrB,KAAK;UAACqC,IAAI,EAAC,gBAAgB;UAACC,OAAO,eAAEjB,OAAA,CAACT,QAAQ;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDrB,OAAA,CAACrB,KAAK;UAACqC,IAAI,EAAC,uBAAuB;UAACC,OAAO,eAAEjB,OAAA,CAACP,cAAc;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnErB,OAAA,CAACrB,KAAK;UAACqC,IAAI,EAAC,2BAA2B;UAACC,OAAO,eAAEjB,OAAA,CAACN,iBAAiB;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1ErB,OAAA,CAACrB,KAAK;UAACqC,IAAI,EAAC,oBAAoB;UAACC,OAAO,eAAEjB,OAAA,CAACR,WAAW;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DrB,OAAA,CAACrB,KAAK;UAACqC,IAAI,EAAC,sBAAsB;UAACC,OAAO,eAAEjB,OAAA,CAACL,aAAa;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjErB,OAAA,CAACrB,KAAK;UAACqC,IAAI,EAAC,sBAAsB;UAACC,OAAO,eAAEjB,OAAA,CAACJ,iBAAiB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrErB,OAAA,CAACrB,KAAK;UAACqC,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEjB,OAAA,CAACH,QAAQ;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDrB,OAAA,CAACrB,KAAK;UAACqC,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEjB,OAAA,CAACF,YAAY;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,eAC7C,CACH,EAGAhB,KAAK,IAAIG,IAAI,KAAK,SAAS,iBAAIR,OAAA,CAACrB,KAAK;QAACqC,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEjB,OAAA,CAAChB,UAAU;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAG1EhB,KAAK,IAAIG,IAAI,KAAK,SAAS,iBAAIR,OAAA,CAACrB,KAAK;QAACqC,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEjB,OAAA,CAACf,WAAW;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,eACTrB,OAAA,CAACX,cAAc;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eAClB,CAAC;AAEP;AAACjB,EAAA,CAlDQD,SAAS;EAAA,QAGCtB,WAAW;AAAA;AAAAyC,EAAA,GAHrBnB,SAAS;AAoDlB,SAASoB,GAAGA,CAAA,EAAG;EACb,oBACEvB,OAAA,CAACZ,mBAAmB;IAAA2B,QAAA,eACpBf,OAAA,CAACb,oBAAoB;MAAA4B,QAAA,eACnBf,OAAA,CAACvB,aAAa;QAAAsC,QAAA,eACZf,OAAA,CAACG,SAAS;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAE1B;AAACG,GAAA,GAVQD,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}