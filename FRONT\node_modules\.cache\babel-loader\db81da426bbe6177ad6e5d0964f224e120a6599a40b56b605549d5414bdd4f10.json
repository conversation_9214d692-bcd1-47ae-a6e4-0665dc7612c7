{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\nimport { useEffect, useRef } from 'react';\nimport AuthRoutes from './routes/AuthRoutes.jsx';\nimport UserRoutes from './routes/UserRoutes.jsx';\nimport AdminRoutes from './routes/AdminRoutes.jsx';\nimport PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';\nimport { NotificationProvider } from './context/NotificationContext';\nimport { PermissionsProvider } from './context/PermissionsContext';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\nimport '../src/assets/styles/custom.css';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AppRouter() {\n  _s();\n  const token = localStorage.getItem('token');\n  const role = localStorage.getItem('role');\n  const navigate = useNavigate();\n  const initialLoadRef = useRef(true);\n  useEffect(() => {\n    // Only redirect on initial load\n    if (initialLoadRef.current && token && window.location.pathname === '/') {\n      initialLoadRef.current = false;\n      if (role === 'trainee') {\n        navigate('/user/dashboard');\n      } else {\n        // For any other role (admin, trainer, etc.)\n        navigate('/admin/dashboard');\n      }\n    }\n  }, [token, role, navigate]);\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/public/courseDetails/:encodedId\",\n      element: /*#__PURE__*/_jsxDEV(PublicCourseDetails, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 63\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), !token && /*#__PURE__*/_jsxDEV(Route, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(AuthRoutes, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 43\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 18\n    }, this), token && role === 'trainee' && /*#__PURE__*/_jsxDEV(Route, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(UserRoutes, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 64\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 39\n    }, this), token && role !== 'trainee' && /*#__PURE__*/_jsxDEV(Route, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(AdminRoutes, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 64\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 39\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n}\n_s(AppRouter, \"hfTeuykHMev6UMiB/hNK1PK1I3o=\", false, function () {\n  return [useNavigate];\n});\n_c = AppRouter;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(PermissionsProvider, {\n    children: /*#__PURE__*/_jsxDEV(NotificationProvider, {\n      children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        children: /*#__PURE__*/_jsxDEV(AppRouter, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppRouter\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Navigate", "useNavigate", "useEffect", "useRef", "AuthRoutes", "UserRoutes", "AdminRoutes", "PublicCourseDetails", "NotificationProvider", "PermissionsProvider", "jsxDEV", "_jsxDEV", "AppRouter", "_s", "token", "localStorage", "getItem", "role", "navigate", "initialLoadRef", "current", "window", "location", "pathname", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/App.js"], "sourcesContent": ["import { BrowserRouter, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\nimport { useEffect, useRef } from 'react';\n\nimport AuthRoutes from './routes/AuthRoutes.jsx';\nimport UserRoutes from './routes/UserRoutes.jsx';\nimport AdminRoutes from './routes/AdminRoutes.jsx';\nimport PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';\nimport { NotificationProvider } from './context/NotificationContext';\nimport { PermissionsProvider } from './context/PermissionsContext';\n\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\nimport '../src/assets/styles/custom.css';\nimport './App.css';\n\nfunction AppRouter() {\n  const token = localStorage.getItem('token');\n  const role = localStorage.getItem('role');\n  const navigate = useNavigate();\n  const initialLoadRef = useRef(true);\n  \n  useEffect(() => {\n    // Only redirect on initial load\n    if (initialLoadRef.current && token && window.location.pathname === '/') {\n      initialLoadRef.current = false;\n      if (role === 'trainee') {\n        navigate('/user/dashboard');\n      } else {\n        // For any other role (admin, trainer, etc.)\n        navigate('/admin/dashboard');\n      }\n    }\n  }, [token, role, navigate]);\n\n  return (\n    <Routes>\n      {/* Public routes - accessible without authentication */}\n      <Route path=\"/public/courseDetails/:encodedId\" element={<PublicCourseDetails />} />\n\n      {/* Protected routes based on authentication */}\n      {!token && <Route path=\"*\" element={<AuthRoutes />} />}\n      {token && role === 'trainee' && <Route path=\"*\" element={<UserRoutes />} />}\n      {token && role !== 'trainee' && <Route path=\"*\" element={<AdminRoutes />} />}\n    </Routes>\n  );\n}\n\nfunction App() {\n  return (\n    <PermissionsProvider>\n    <NotificationProvider>\n      <BrowserRouter>\n        <AppRouter />\n      </BrowserRouter>\n    </NotificationProvider>\n    </PermissionsProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACtF,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEzC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,mBAAmB,MAAM,wCAAwC;AACxE,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,mBAAmB,QAAQ,8BAA8B;AAElE,OAAO,sCAAsC;AAC7C,OAAO,2CAA2C;AAClD,OAAO,iCAAiC;AACxC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,MAAMC,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EACzC,MAAME,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,cAAc,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACd;IACA,IAAIiB,cAAc,CAACC,OAAO,IAAIN,KAAK,IAAIO,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,GAAG,EAAE;MACvEJ,cAAc,CAACC,OAAO,GAAG,KAAK;MAC9B,IAAIH,IAAI,KAAK,SAAS,EAAE;QACtBC,QAAQ,CAAC,iBAAiB,CAAC;MAC7B,CAAC,MAAM;QACL;QACAA,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;IACF;EACF,CAAC,EAAE,CAACJ,KAAK,EAAEG,IAAI,EAAEC,QAAQ,CAAC,CAAC;EAE3B,oBACEP,OAAA,CAACb,MAAM;IAAA0B,QAAA,gBAELb,OAAA,CAACZ,KAAK;MAAC0B,IAAI,EAAC,kCAAkC;MAACC,OAAO,eAAEf,OAAA,CAACJ,mBAAmB;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGlF,CAAChB,KAAK,iBAAIH,OAAA,CAACZ,KAAK;MAAC0B,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEf,OAAA,CAACP,UAAU;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACrDhB,KAAK,IAAIG,IAAI,KAAK,SAAS,iBAAIN,OAAA,CAACZ,KAAK;MAAC0B,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEf,OAAA,CAACN,UAAU;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC1EhB,KAAK,IAAIG,IAAI,KAAK,SAAS,iBAAIN,OAAA,CAACZ,KAAK;MAAC0B,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEf,OAAA,CAACL,WAAW;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtE,CAAC;AAEb;AAACjB,EAAA,CA9BQD,SAAS;EAAA,QAGCX,WAAW;AAAA;AAAA8B,EAAA,GAHrBnB,SAAS;AAgClB,SAASoB,GAAGA,CAAA,EAAG;EACb,oBACErB,OAAA,CAACF,mBAAmB;IAAAe,QAAA,eACpBb,OAAA,CAACH,oBAAoB;MAAAgB,QAAA,eACnBb,OAAA,CAACd,aAAa;QAAA2B,QAAA,eACZb,OAAA,CAACC,SAAS;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAE1B;AAACG,GAAA,GAVQD,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}