import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { validateEmail, validatePassword, validateForm } from '../../utils/validation';
import { getLogoByDomainAndAlt } from '../../utils/logoUtils';
import { loginApi, sendOTPApi, ActiveAccountOTPFromLoginPage } from '../../services/authService';
import { usePermissions } from '../../context/PermissionsContext';


function Login() {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { setPermissionsFromAPI } = usePermissions();

  // Validation Rules
  const validationRules = {
    email: validateEmail,
    password: validatePassword
  };

  // Handle Change
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // Update both form data and clear errors in a single function
    const newFormData = { ...formData, [name]: value };
    const newErrors = { ...errors, [name]: undefined };
    
    setFormData(newFormData);
    setErrors(newErrors);
  };

  // Handle Submit
  const handleSubmit = async (e) => {
    e.preventDefault();
  
    const validation = validateForm(formData, validationRules);
    if (validation.isValid) {
      setIsLoading(true);
  
      const payload = {
        email: formData.email,
        password: formData.password,
        organization_url: window.location.origin
      };
  
      console.log("Payload being sent to API:-------------------------", payload);

      try {
        console.log("Payload being sent to API:", payload);
  
        const response = await loginApi(payload);
        console.log("API response:", response);
  
        // ⛔ Email not verified - redirect to OTP page
        if (response?.is_email_verified === 0) {
          console.log("Email not verified, sending OTP and redirecting to activation page");

          try {
            // Call sendOTP API to send activation OTP
            const otpPayload = {
              email: formData.email,
              domain: window.location.origin
            };

            await ActiveAccountOTPFromLoginPage(otpPayload);
            console.log("OTP sent successfully for account activation");

           toast.success('Account registered but not activated. Please check your email.', {
              position: "top-center",
              autoClose: 2000,
              hideProgressBar: false,
              closeOnClick: true,
              pauseOnHover: true,
              draggable: true,
              onClose: () => {
                setIsLoading(false); // Turn off loader only when redirecting
                navigate('/auth/active-account', {
                  state: {
                    email: formData.email
                  }
                });
              }
            });

            
          } catch (otpError) {
            console.error("Error sending OTP:", otpError);
            toast.error('Failed to send OTP. Please try again.', {
              position: "top-center",
              autoClose: 3000
            });
            setIsLoading(false);
          }
          return;
        }
  
        // Always wait 500ms before showing result
        await new Promise(resolve => setTimeout(resolve, 500));
  
        if (response.success) {
          // Set permissions in context
          setPermissionsFromAPI(response.data.permissions);
  
          localStorage.setItem('token', response.data.access_token);
          localStorage.setItem('refresh_token', response.data.refresh_token);
          localStorage.setItem('user', JSON.stringify(response.data.user_details));
          localStorage.setItem('organization_details', JSON.stringify(response.data.dashboard));
          localStorage.setItem('role', response.data.role);
  
          toast.success(response.data.message || 'Login successful', {
            position: "top-center",
            autoClose: 1000,
            onClose: () => {
              console.log("Role:", response.data.role);
              if (response.data.role === 'trainee') {
                navigate('/user/dashboard');
              } else {
                navigate('/admin/dashboard');
              }
            }
          });
        } else {
          toast.error(response.message || "Login failed", {
            position: "top-center",
            autoClose: 5000
          });
          setIsLoading(false);
        }
  
      } catch (error) {
        console.error("Login API error:", error?.response?.data);
  
        await new Promise(resolve => setTimeout(resolve, 500));
  
        toast.error(error?.response?.data?.message || "Something went wrong", {
          position: "top-center",
          autoClose: 5000
        });
        setIsLoading(false);
      }
    } else {
      setErrors(validation.errors);
    }
  };
  
  

  return (
    <div className="auth-container">
      {/* Logo  */}
      <div className="auth-logo">
        <img src={getLogoByDomainAndAlt().logo} alt={getLogoByDomainAndAlt().alt} style={{ width: '180px', height: 'auto' }} />
      </div>
      {/* Login Card */}
      <div className="auth-card">
        <div className="auth-header">
          <h2>Login to LMS</h2>
          <p>Enter your email & password to login</p>
        </div>
        
        <form onSubmit={handleSubmit} className="auth-form">
          <div className="auth-form-group">
            <label htmlFor="email">Email Address</label>
            <div className="auth-input-wrapper">
              <Icon icon="mdi:email-outline" className="auth-input-icon" />
              <input 
                type="text"
                id="email" 
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>" 
                className={`auth-input ${errors.email ? 'error' : ''}`}
                disabled={isLoading}
              />
            </div>
            {errors.email && <span className="auth-error-message">{errors.email[0]}</span>}
          </div>
          
          <div className="auth-form-group">
            <label htmlFor="password">Password</label>
            <div className="auth-input-wrapper">
              <Icon icon="mdi:lock-outline" className="auth-input-icon" />
              <input 
                type={showPassword ? "text" : "password"} 
                id="password" 
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="••••••" 
                className={`auth-input ${errors.password ? 'error' : ''}`}
                disabled={isLoading}
              />
              <button 
                type="button" 
                className="auth-password-toggle" 
                onClick={() => setShowPassword(!showPassword)}
                aria-label={showPassword ? "Hide password" : "Show password"}
                disabled={isLoading}
              >
                <Icon icon={showPassword ? "mdi:eye-off-outline" : "mdi:eye-outline"} />
              </button>
            </div>
            {errors.password && <span className="auth-error-message">{errors.password[0]}</span>}
          </div>
          
          <div className="auth-forgot-password">
            <a href="/auth/forgot-password" className={isLoading ? 'disabled-link' : ''}>Forgot Password?</a>
          </div>
          
          <button 
            type="submit" 
            className={`btn btn-primary ${isLoading ? 'loading' : ''}`}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="spinner"></div>
                Logging in...
              </>
            ) : (
              'Login'
            )}
          </button>
          
          <div className="auth-options">
            Don't have an account? <a href="/auth/register" className={isLoading ? 'disabled-link' : ''}>Create Account</a>
          </div>
        </form>
      </div>
    </div>
  );
}

export default Login;