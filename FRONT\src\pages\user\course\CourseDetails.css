.course-details-header {
    background: linear-gradient(to right, #FF69B4, #FF4500);
    color: white;
    padding: 4rem 0;
    margin-bottom: 2rem;
}

.course-details-header h3 {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #fff;
}

.course-details-header .font-size-8 {
    font-size: 1.2rem;
    opacity: 0.95;
    margin-bottom: 2rem;
    font-weight: 500;
}

.course-details-header .iconify {
    font-size: 1.4rem;
    vertical-align: middle;
    margin-right: 0.5rem;
}

.course-details-header .d-flex.align-items-center {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
}

.course-details-header .text-warning {
    color: #FFD700 !important;
}

.course-details-header .breadcrumb {
    background: transparent;
}

.course-details-header .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.course-details-header .breadcrumb-item.active {
    color: white;
}

.course-details-header .text-muted,
.course-details-header .lead {
    color: rgba(255, 255, 255, 0.9) !important;
}

.course-details-header .rating-stars {
    color: #FFD700;
}

.course-details-container {
    background-color: white;
    border-radius: 8px;
    margin-top: -60px;
    padding: 2rem;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}


.course-card {
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.course-card .card-img-top {
    border-radius: 12px 12px 0 0;
}

.price-tag {
    font-size: 2.5rem;
    font-weight: bold;
    color: #FF4500;
}

.original-price {
    text-decoration: line-through;
    color: #6c757d;
    font-size: 1.1rem;
}

.instructor-image {
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.instructor-image img {
    display: block;
}

.font-size-1 {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.9);
}

.font-size-1:first-child {
    font-weight: 600;
}

/* Button styling */
.btn-primary {
    background: #4169E1;
    border: none;
    padding: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #3457b1;
    transform: translateY(-2px);
}

.btn-outline-primary {
    border-color: #4169E1;
    color: #4169E1;
    padding: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: #4169E1;
    transform: translateY(-2px);
}

.course-includes {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px;
    margin: 1.5rem 0;
}

.course-includes h4 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1rem;
}

.course-includes li {
    color: #495057;
    font-size: 0.95rem;
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.course-includes .text-muted {
    color: #6c757d !important;
    font-size: 1.1rem;
}

.course-includes li:hover {
    background-color: #f1f3f5;
    border-radius: 6px;
    padding-left: 0.5rem;
    transition: all 0.2s ease;
}

.watch-now-btn, .paid-btn {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.free-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
}

.free-btn:hover {
    background-color: #45a049;
    transform: translateY(-2px);
}

.paid-btn {
    background-color: #4169E1;
    color: white;
    border: none;
}

.paid-btn:hover {
    background-color: #3457b1;
    transform: translateY(-2px);
}

.btn-icon {
    font-size: 1.25rem;
}

.learning-points {
    background-color: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
}

.learning-point {
    transition: all 0.2s ease;
    padding: 0.5rem 1rem;
    border-radius: 8px;
}

/* .learning-point:hover {
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
} */

.learning-point .iconify {
    font-size: 1.25rem;
    color: #28a745;
}

.learning-point span {
    color: #2c3e50;
    font-size: 1rem;
    line-height: 1.5;
}

.about-course {
    border-top: 1px solid #e9ecef;
    padding-top: 2rem;
}

.about-course h3 {
    color: #2c3e50;
    font-weight: 600;
}

.about-content {
    color: #6c757d;
    font-size: 1rem;
    line-height: 1.7;
}

.about-content p {
    margin-bottom: 1.5rem;
}

.course-content {
    margin-top: 2rem;
}

.module-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.module-header {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.module-header:hover {
    background-color: #e9ecef;
}

.module-header svg {
    color: #6c757d;
    font-size: 0.875rem;
}

.module-meta {
    font-size: 0.875rem;
}

.lecture-item {
    border-top: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.lecture-item:hover {
    background-color: #f8f9fa;
}

.lecture-item svg {
    color: #6c757d;
    font-size: 1.25rem;
}

.btn-preview {
    color: #ff6b6b;
    border: 1px solid #ff6b6b;
    padding: 2px 12px;
    font-size: 0.75rem;
    border-radius: 4px;
    background: transparent;
    transition: all 0.2s ease;
}

.btn-preview:hover {
    background-color: #ff6b6b;
    color: white;
}

.instructor-profile {
    background-color: #fff;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.instructor-image-lg img {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.instructor-info h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.instructor-info .rating-stars {
    display: inline-flex;
    gap: 2px;
}

.instructor-info .rating-stars .iconify {
    font-size: 1.2rem;
    color: #FFD700;
}

.social-links {
    display: flex;
    gap: 0.5rem;
}

.social-links .btn {
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.social-links .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@media screen and (max-width: 768px) {
    .course-details-header {
        padding: 2rem 0;
    }

    .course-details-header h3 {
        font-size: 2rem;
    }
    
    .course-details-container{
        padding: 1rem;
    }
    
    .instructor-image-lg img {
        width: 150px !important;
        height: 150px !important;
        margin: 0 auto;
        display: block;
    }

    .instructor-info {
        text-align: center;
    }

    .instructor-info .d-flex {
        justify-content: center;
    }

    .social-links {
        justify-content: center;
        margin-top: 1rem;
    }

    .instructor-profile {
        padding: 1rem;
    }
}

@media screen and (max-width: 576px) {
    .instructor-info .d-flex.flex-wrap {
        flex-direction: column;
        align-items: center;
        gap: 1rem !important;
    }
}