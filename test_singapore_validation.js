// Test Singapore IC/FIN Validation

// Singapore IC/FIN Validation Function (same as in orgUser.js)
function validateSingaporeIdentity(identityNumber) {
  if (!identityNumber || typeof identityNumber !== 'string') {
    return { isValid: false, error: 'Identity number is required', type: null };
  }

  // Remove spaces and convert to uppercase
  const cleanId = identityNumber.replace(/\s/g, '').toUpperCase();
  
  // Check basic format: 1 letter + 7 digits + 1 letter
  const icFinPattern = /^[STFGM]\d{7}[A-Z]$/;
  if (!icFinPattern.test(cleanId)) {
    return { isValid: false, error: 'Invalid IC/FIN format. Must be: Letter + 7 digits + Letter (e.g., ********A)', type: null };
  }

  const firstLetter = cleanId[0];
  const digits = cleanId.substring(1, 8);
  const lastLetter = cleanId[8];

  // Determine identity type
  let identityType;
  let weights;
  let checksumLetters;

  if (['S', 'T'].includes(firstLetter)) {
    identityType = 'IC';
    weights = [2, 7, 6, 5, 4, 3, 2];
    checksumLetters = firstLetter === 'S' ? 'ABCDEFGHIZJ' : 'GFEDCBAHIZJ';
  } else if (['F', 'G'].includes(firstLetter)) {
    identityType = 'FIN';
    weights = [2, 7, 6, 5, 4, 3, 2];
    checksumLetters = firstLetter === 'F' ? 'XWUTRQPNMLK' : 'RPNMLKJIHGF';
  } else if (firstLetter === 'M') {
    identityType = 'FIN';
    weights = [2, 7, 6, 5, 4, 3, 2];
    checksumLetters = 'XWUTRQPNMLK';
  } else {
    return { isValid: false, error: 'Invalid first letter. Must be S, T, F, G, or M', type: null };
  }

  // Calculate checksum
  let sum = 0;
  for (let i = 0; i < 7; i++) {
    sum += parseInt(digits[i]) * weights[i];
  }

  const remainder = sum % 11;
  const expectedLetter = checksumLetters[remainder];

  if (lastLetter !== expectedLetter) {
    return { 
      isValid: false, 
      error: `Invalid checksum. Expected ${expectedLetter}, got ${lastLetter}`, 
      type: identityType 
    };
  }

  return { 
    isValid: true, 
    error: null, 
    type: identityType,
    formatted: cleanId
  };
}

// Test cases
console.log('🧪 Testing Singapore IC/FIN Validation\n');

const testCases = [
  // Valid IC numbers
  '********D',  // Valid Singapore IC
  '*********',  // Valid Singapore IC (T series)
  
  // Valid FIN numbers  
  '*********',  // Valid FIN (F series)
  '*********',  // Valid FIN (G series)
  'M1234567K',  // Valid FIN (M series)
  
  // Invalid cases
  '********X',  // Wrong checksum
  'A1234567D',  // Invalid first letter
  'S123456D',   // Too short
  '********8D', // Too long
  '********',   // Missing last letter
  '1234567D',   // Missing first letter
  '',           // Empty
  null,         // Null
  'invalid'     // Invalid format
];

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase || 'null/empty'}`);
  const result = validateSingaporeIdentity(testCase);
  
  if (result.isValid) {
    console.log(`✅ VALID ${result.type}: ${result.formatted}`);
  } else {
    console.log(`❌ INVALID: ${result.error}`);
  }
  console.log('---');
});

// Test login with validation
async function testLoginWithValidation() {
  try {
    console.log('\n🧪 Testing login with Singapore validation...');
    
    const loginData = {
      email: '<EMAIL>', // Replace with a valid test email
      password: 'testpassword123', // Replace with a valid test password
      url: 'http://test.localhost:3001'
    };

    const response = await axios.post('http://localhost:5001/org/user_login', loginData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Login successful with Singapore validation!');
    console.log('Response:', response.data);
    
  } catch (error) {
    if (error.response) {
      console.log('❌ Login failed with status:', error.response.status);
      console.log('Error message:', error.response.data);
    } else {
      console.log('❌ Network error:', error.message);
    }
  }
}

// Run tests
console.log('\n' + '='.repeat(50));
console.log('Singapore IC/FIN Validation Test Complete');
console.log('='.repeat(50));

// Uncomment to test login (make sure server is running)
// testLoginWithValidation();
