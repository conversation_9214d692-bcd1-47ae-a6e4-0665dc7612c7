const { response } = require("express");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcrypt");
const { mysqlServerConnection } = require("../../../db/db");
const createdb = require("../../../superAdmin/db/utils/createOrganizationDb");

const {
  Validate,
  hashPassword,
  generateAccessToken,
  generateRefreshToken,
  sendEmail,
  LogsHandler,
} = require("../../../tools/tools");
const {
  sendMagicLink,
  sendForgotPasswordLink,
  sendOTP,
  sentEmailNotification,
  registerOTP,
  forgotPasswordOTP,
  ActiveAccountOTPFromLoginPage
} = require("../../../tools/emailtemplates");
const { body } = require("express-validator");
const crypto = require("crypto");
const maindb = process.env.MAIN_DB;
const { OAuth2Client } = require("google-auth-library");



const organizationLoginUser = async (req, res) => {
  console.log("📥 ➡️# organizationLoginUser called");

  try {
    let { email, password, fcm_token, organization_url } = req.body;

    console.log("🟡 Incoming Request Body:", req.body);

    email = email?.trim().toLowerCase();

    if (!email || !password) {
      console.log("❌ Missing email or password");
      return res.status(400).json({
        success: false,
        status: 404,
        message: "Email and password are required.",
      });
    }
    console.log("✅ Email and password received");

    if (!organization_url) {
      console.log("❌ Missing organization URL");
      return res.status(400).json({
        success: false,
        status: 404,
        message: "Organization URL is required.",
      });
    }
    console.log("✅ Organization URL received");

    console.log("🔍 Looking up organization...");
    const [dbmain] = await mysqlServerConnection.query(
      `SELECT db_name, org_logo_url, org_favicon_url, name 
       FROM ${maindb}.organization 
       WHERE auth_sub_domain = ?`,
      [organization_url]
    );

    if (dbmain.length === 0) {
      console.log("❌ Organization not found");
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Invalid organization URL.",
      });
    }

    const orgdb = dbmain[0];
    console.log("✅ Organization found:", orgdb);
    console.log("✅ Organization DB Name:", orgdb.db_name);

    console.log("📧 Checking if email exists...");
    const [checkemail] = await mysqlServerConnection.query(
      `SELECT * FROM ${orgdb.db_name}.users WHERE email = ?`,
      [email]
    );

    if (checkemail.length === 0) {
      console.log("❌ Email not registered in users table");
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Email is not registered.",
      });
    }
    console.log("✅ Email exists in users table");

    console.log("👤 Fetching user and role...");
    const [userRows] = await mysqlServerConnection.query(
      `SELECT users.*, roles.name AS role, roles.id AS role_id
       FROM ${orgdb.db_name}.users
       JOIN ${orgdb.db_name}.user_roles ON users.id = user_roles.user_id
       JOIN ${orgdb.db_name}.roles ON user_roles.role_id = roles.id
       WHERE users.email = ?`,
      [email]
    );

    console.log("🔍 User and role query executed", userRows);

    if (userRows.length === 0) {
      console.log("❌ User + role not found");
      return res
        .status(401)
        .json({ success: false, status: 404, message: "User not found." });
    }

    const user = userRows[0];
    console.log("✅ User with role found:", {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      role_id: user.role_id,
    });

    // ✅ Custom check for is_email_verified === 0
    if (user.is_email_verified === 0) {
      console.log("❌ Email is not verified (value is 0)");
      return res.status(200).json({
        success: true,
        is_email_verified: 0,
        message: "Please verify your email before logging in.",
      });
    }

    if (user.is_deleted === 1) {
      console.log("❌ User is marked as deleted");
      return res.status(403).json({
        success: false,
        status: 404,
        message: "Your account has been deleted.",
      });
    }

    if (user.is_blocked === 1) {
      console.log("❌ User is blocked");
      return res.status(403).json({
        success: false,
        status: 404,
        message: "Your account is blocked. Please contact the admin.",
      });
    }

    if (user.is_email_verified !== 1) {
      console.log("❌ Email not verified");
      return res.status(403).json({
        success: false,
        status: 404,
        message: "Please activate your email to continue.",
      });
    }
    console.log("✅ Email is verified");

    validate 


    console.log("🔐 Verifying password...");
    const passwordMatch = await bcrypt.compare(password, user.password);
    if (!passwordMatch) {
      console.log("❌ Password does not match");
      return res
        .status(401)
        .json({ success: false, status: 404, message: "Invalid password." });
    }
    console.log("✅ Password matched");

    console.log("📝 Logging login event...");
    LogsHandler(orgdb.db_name, user.id, "Login", `${user.name} is Logged In`);
    console.log("✅ Login event logged");

    console.log("🧹 Cleaning user data...");
    const cleanedUser = (({
      is_blocked,
      is_deleted,
      password,
      db_name,
      company_description,
      ic_no,
      fin_no,
      gender,
      date_of_birth,
      date_joined,
      pwm_rank,
      job_title,
      ...rest
    }) => rest)(user);
    console.log("✅ Cleaned User Object:", cleanedUser);

    console.log("🔑 Generating access and refresh tokens...");
    const accessToken = await generateAccessToken({
      id: user.id,
      db_name: orgdb.db_name,
      name: user.name,
      email: user.email,
      role: user.role,
      role_id: user.role_id,
    });

    const refreshToken = await generateRefreshToken({
      id: user.id,
      db_name: orgdb.db_name,
      name: user.name,
      email: user.email,
      role: user.role,
      role_id: user.role_id,
    });
    console.log("✅ Tokens generated successfully");

    const [permissionsResult] = await mysqlServerConnection.query(
      `SELECT rp.permission_id, rp.is_granted, p.slug
       FROM ${orgdb.db_name}.role_permissions rp
       JOIN ${maindb}.permission_modules_and_actions_mapping p 
       ON rp.permission_id = p.id
       WHERE rp.role_id = ?`,
      [user.role_id]
    );

    console.log("✅ Permissions query executed");
    console.log("📊 Number of permissions found:", permissionsResult.length);

    console.log("🔄 Mapping permissions...");
    const permissions = permissionsResult.map(permission => ({
      permission_id: permission.permission_id,
      slug: permission.slug,
      is_granted: permission.is_granted
    }));
    console.log("✅ Permissions mapped successfully");

    console.log("🔧 Preparing response payload...");
    const responsePayload = {
      success: true,
      data: {
        message: "Login Successful.",
        access_token: accessToken,
        refresh_token: refreshToken,
        role: user.role,
        user_details: cleanedUser,
        permissions: permissions,
        dashboard: {
          logo: orgdb.org_logo_url,
          favicon: orgdb.org_favicon_url,
          name: orgdb.name,
        },
      },
    };
    console.log("✅ Response payload prepared");
    console.log("📦 Response payload size:", JSON.stringify(responsePayload).length, "bytes");

    console.log("📤 Attempting to send response...");
    try {
      return res.status(200).json(responsePayload);
    } catch (error) {
      console.error("❌ Error sending response:", error);
      console.error("🔍 Error details:", {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      throw error;
    }
    console.log("✅ Response sent successfully");
  } catch (error) {
    console.error("❌ Error in organizationLoginUser:", error);
    console.error("🔍 Detailed error information:", {
      name: error.name,
      message: error.message,
      code: error.code,
      stack: error.stack,
      response: error.response ? {
        status: error.response.status,
        data: error.response.data
      } : null
    });

    if (res.headersSent) {
      console.error("⚠️ Headers already sent, cannot send error response");
      return;
    }

    console.log("📤 Sending error response...");
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
      code: error.code || 'UNKNOWN'
    });
  }
};


// -------------------------------------------------------------------------------------


module.exports = {
  organizationLoginUser,
};
