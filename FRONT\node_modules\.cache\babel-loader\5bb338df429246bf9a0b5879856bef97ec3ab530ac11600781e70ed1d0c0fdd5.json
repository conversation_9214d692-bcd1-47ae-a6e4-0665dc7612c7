{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\routes\\\\AuthRoutes.jsx\";\nimport React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\n\n// Import auth pages\nimport Login from '../pages/auth/Login';\nimport Register from '../pages/auth/Register';\nimport RegisterOTP from '../pages/auth/RegisterOTP';\nimport ForgotPassword from '../pages/auth/ForgotPassword';\nimport ForgotPasswordOTP from '../pages/auth/ForgotPasswordOTP';\nimport ResetPassword from '../pages/auth/ResetPassword';\nimport ActiveAccountPage from '../pages/auth/ActiveAccountPage';\nimport Error401 from '../pages/error/Error401';\nimport PageNotFound from '../pages/error/PageNotFound';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AuthRoutes() {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 44\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/register\",\n        element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 47\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/forgot-password\",\n        element: /*#__PURE__*/_jsxDEV(ForgotPassword, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 54\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/forgot-password-otp\",\n        element: /*#__PURE__*/_jsxDEV(ForgotPasswordOTP, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 58\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/register-otp\",\n        element: /*#__PURE__*/_jsxDEV(RegisterOTP, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 51\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/reset-password\",\n        element: /*#__PURE__*/_jsxDEV(ResetPassword, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/active-account\",\n        element: /*#__PURE__*/_jsxDEV(ActiveAccountPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/error/401\",\n        element: /*#__PURE__*/_jsxDEV(Error401, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(PageNotFound, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_c = AuthRoutes;\nexport default AuthRoutes;\nvar _c;\n$RefreshReg$(_c, \"AuthRoutes\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "ToastContainer", "<PERSON><PERSON>", "Register", "RegisterOTP", "ForgotPassword", "ForgotPasswordOTP", "ResetPassword", "ActiveAccountPage", "Error401", "PageNotFound", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AuthRoutes", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/routes/AuthRoutes.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { Routes, Route } from 'react-router-dom';\r\nimport { ToastContainer } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\n\r\n// Import auth pages\r\nimport Login from '../pages/auth/Login';\r\nimport Register from '../pages/auth/Register';\r\nimport RegisterOTP from '../pages/auth/RegisterOTP';\r\nimport ForgotPassword from '../pages/auth/ForgotPassword';\r\nimport ForgotPasswordOTP from '../pages/auth/ForgotPasswordOTP';\r\nimport ResetPassword from '../pages/auth/ResetPassword';\r\nimport ActiveAccountPage from '../pages/auth/ActiveAccountPage';\r\nimport Error401 from '../pages/error/Error401';\r\nimport PageNotFound from '../pages/error/PageNotFound';\r\n\r\n\r\n\r\nfunction AuthRoutes() {\r\n  return (\r\n    <>\r\n      <Routes>\r\n        <Route path=\"/\" element={<Login />} />\r\n        <Route path=\"/auth/login\" element={<Login />} />\r\n        <Route path=\"/auth/register\" element={<Register />} />\r\n        <Route path=\"/auth/forgot-password\" element={<ForgotPassword />} />\r\n        <Route path=\"/auth/forgot-password-otp\" element={<ForgotPasswordOTP />} />\r\n        <Route path=\"/auth/register-otp\" element={<RegisterOTP />} />\r\n        <Route path=\"/auth/reset-password\" element={<ResetPassword />} />\r\n        <Route path=\"/auth/active-account\" element={<ActiveAccountPage />} />\r\n\r\n\r\n        {/* Error Routes */}\r\n        <Route path=\"/error/401\" element={<Error401 />} />\r\n        <Route path=\"*\" element={<PageNotFound />} />\r\n      </Routes>\r\n      <ToastContainer />\r\n    </>\r\n  );\r\n}\r\n\r\nexport default AuthRoutes;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;;AAE9C;AACA,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,YAAY,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAIvD,SAASC,UAAUA,CAAA,EAAG;EACpB,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACEJ,OAAA,CAACb,MAAM;MAAAiB,QAAA,gBACLJ,OAAA,CAACZ,KAAK;QAACiB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEN,OAAA,CAACV,KAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtCV,OAAA,CAACZ,KAAK;QAACiB,IAAI,EAAC,aAAa;QAACC,OAAO,eAAEN,OAAA,CAACV,KAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChDV,OAAA,CAACZ,KAAK;QAACiB,IAAI,EAAC,gBAAgB;QAACC,OAAO,eAAEN,OAAA,CAACT,QAAQ;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDV,OAAA,CAACZ,KAAK;QAACiB,IAAI,EAAC,uBAAuB;QAACC,OAAO,eAAEN,OAAA,CAACP,cAAc;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnEV,OAAA,CAACZ,KAAK;QAACiB,IAAI,EAAC,2BAA2B;QAACC,OAAO,eAAEN,OAAA,CAACN,iBAAiB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1EV,OAAA,CAACZ,KAAK;QAACiB,IAAI,EAAC,oBAAoB;QAACC,OAAO,eAAEN,OAAA,CAACR,WAAW;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DV,OAAA,CAACZ,KAAK;QAACiB,IAAI,EAAC,sBAAsB;QAACC,OAAO,eAAEN,OAAA,CAACL,aAAa;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjEV,OAAA,CAACZ,KAAK;QAACiB,IAAI,EAAC,sBAAsB;QAACC,OAAO,eAAEN,OAAA,CAACJ,iBAAiB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAIrEV,OAAA,CAACZ,KAAK;QAACiB,IAAI,EAAC,YAAY;QAACC,OAAO,eAAEN,OAAA,CAACH,QAAQ;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDV,OAAA,CAACZ,KAAK;QAACiB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEN,OAAA,CAACF,YAAY;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eACTV,OAAA,CAACX,cAAc;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eAClB,CAAC;AAEP;AAACC,EAAA,GArBQR,UAAU;AAuBnB,eAAeA,UAAU;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}