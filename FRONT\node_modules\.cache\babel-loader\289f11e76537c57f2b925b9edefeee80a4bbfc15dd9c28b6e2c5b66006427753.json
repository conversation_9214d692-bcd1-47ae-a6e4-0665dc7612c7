{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\routes\\\\AuthRoutes.jsx\";\nimport React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\n\n// Import auth pages\nimport Login from '../pages/auth/Login';\nimport Register from '../pages/auth/Register';\nimport RegisterOTP from '../pages/auth/RegisterOTP';\nimport ForgotPassword from '../pages/auth/ForgotPassword';\nimport ForgotPasswordOTP from '../pages/auth/ForgotPasswordOTP';\nimport ResetPassword from '../pages/auth/ResetPassword';\nimport ActiveAccountPage from '../pages/auth/ActiveAccountPage';\nimport Error401 from '../pages/error/Error401';\nimport PageNotFound from '../pages/error/PageNotFound';\nimport PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AuthRoutes() {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 44\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/register\",\n        element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 47\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/forgot-password\",\n        element: /*#__PURE__*/_jsxDEV(ForgotPassword, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 54\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/forgot-password-otp\",\n        element: /*#__PURE__*/_jsxDEV(ForgotPasswordOTP, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 58\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/register-otp\",\n        element: /*#__PURE__*/_jsxDEV(RegisterOTP, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 51\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/reset-password\",\n        element: /*#__PURE__*/_jsxDEV(ResetPassword, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/active-account\",\n        element: /*#__PURE__*/_jsxDEV(ActiveAccountPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/public/courseDetails/:encodedId\",\n        element: /*#__PURE__*/_jsxDEV(PublicCourseDetails, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 73\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/error/401\",\n        element: /*#__PURE__*/_jsxDEV(Error401, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(PageNotFound, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_c = AuthRoutes;\nexport default AuthRoutes;\nvar _c;\n$RefreshReg$(_c, \"AuthRoutes\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "ToastContainer", "<PERSON><PERSON>", "Register", "RegisterOTP", "ForgotPassword", "ForgotPasswordOTP", "ResetPassword", "ActiveAccountPage", "Error401", "PageNotFound", "PublicCourseDetails", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AuthRoutes", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/routes/AuthRoutes.jsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\n\n// Import auth pages\nimport Login from '../pages/auth/Login';\nimport Register from '../pages/auth/Register';\nimport RegisterOTP from '../pages/auth/RegisterOTP';\nimport ForgotPassword from '../pages/auth/ForgotPassword';\nimport ForgotPasswordOTP from '../pages/auth/ForgotPasswordOTP';\nimport ResetPassword from '../pages/auth/ResetPassword';\nimport ActiveAccountPage from '../pages/auth/ActiveAccountPage';\nimport Error401 from '../pages/error/Error401';\nimport PageNotFound from '../pages/error/PageNotFound';\nimport PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';\n\n\n\n\nfunction AuthRoutes() {\n  return (\n    <>\n      <Routes>\n        <Route path=\"/\" element={<Login />} />\n        <Route path=\"/auth/login\" element={<Login />} />\n        <Route path=\"/auth/register\" element={<Register />} />\n        <Route path=\"/auth/forgot-password\" element={<ForgotPassword />} />\n        <Route path=\"/auth/forgot-password-otp\" element={<ForgotPasswordOTP />} />\n        <Route path=\"/auth/register-otp\" element={<RegisterOTP />} />\n        <Route path=\"/auth/reset-password\" element={<ResetPassword />} />\n        <Route path=\"/auth/active-account\" element={<ActiveAccountPage />} />\n                <Route path=\"/public/courseDetails/:encodedId\" element={<PublicCourseDetails />} />\n\n\n\n        {/* Error Routes */}\n        <Route path=\"/error/401\" element={<Error401 />} />\n        <Route path=\"*\" element={<PageNotFound />} />\n      </Routes>\n      <ToastContainer />\n    </>\n  );\n}\n\nexport default AuthRoutes;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;;AAE9C;AACA,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,YAAY,MAAM,6BAA6B;AACtD,OAAOC,mBAAmB,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAKzE,SAASC,UAAUA,CAAA,EAAG;EACpB,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACEJ,OAAA,CAACd,MAAM;MAAAkB,QAAA,gBACLJ,OAAA,CAACb,KAAK;QAACkB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEN,OAAA,CAACX,KAAK;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtCV,OAAA,CAACb,KAAK;QAACkB,IAAI,EAAC,aAAa;QAACC,OAAO,eAAEN,OAAA,CAACX,KAAK;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChDV,OAAA,CAACb,KAAK;QAACkB,IAAI,EAAC,gBAAgB;QAACC,OAAO,eAAEN,OAAA,CAACV,QAAQ;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDV,OAAA,CAACb,KAAK;QAACkB,IAAI,EAAC,uBAAuB;QAACC,OAAO,eAAEN,OAAA,CAACR,cAAc;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnEV,OAAA,CAACb,KAAK;QAACkB,IAAI,EAAC,2BAA2B;QAACC,OAAO,eAAEN,OAAA,CAACP,iBAAiB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1EV,OAAA,CAACb,KAAK;QAACkB,IAAI,EAAC,oBAAoB;QAACC,OAAO,eAAEN,OAAA,CAACT,WAAW;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DV,OAAA,CAACb,KAAK;QAACkB,IAAI,EAAC,sBAAsB;QAACC,OAAO,eAAEN,OAAA,CAACN,aAAa;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjEV,OAAA,CAACb,KAAK;QAACkB,IAAI,EAAC,sBAAsB;QAACC,OAAO,eAAEN,OAAA,CAACL,iBAAiB;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DV,OAAA,CAACb,KAAK;QAACkB,IAAI,EAAC,kCAAkC;QAACC,OAAO,eAAEN,OAAA,CAACF,mBAAmB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAK3FV,OAAA,CAACb,KAAK;QAACkB,IAAI,EAAC,YAAY;QAACC,OAAO,eAAEN,OAAA,CAACJ,QAAQ;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDV,OAAA,CAACb,KAAK;QAACkB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEN,OAAA,CAACH,YAAY;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eACTV,OAAA,CAACZ,cAAc;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eAClB,CAAC;AAEP;AAACC,EAAA,GAvBQR,UAAU;AAyBnB,eAAeA,UAAU;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}