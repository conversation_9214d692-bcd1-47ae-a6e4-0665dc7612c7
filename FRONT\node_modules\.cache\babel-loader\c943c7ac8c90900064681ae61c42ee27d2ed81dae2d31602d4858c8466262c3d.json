{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\nimport { useEffect, useRef } from 'react';\nimport UserRoutes from './routes/UserRoutes.jsx';\nimport AdminRoutes from './routes/AdminRoutes.jsx';\nimport PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';\nimport { NotificationProvider } from './context/NotificationContext';\nimport { PermissionsProvider } from './context/PermissionsContext';\n\n// Import auth pages\nimport Login from './pages/auth/Login';\nimport Register from './pages/auth/Register';\nimport RegisterOTP from './pages/auth/RegisterOTP';\nimport ForgotPassword from './pages/auth/ForgotPassword';\nimport ForgotPasswordOTP from './pages/auth/ForgotPasswordOTP';\nimport ResetPassword from './pages/auth/ResetPassword';\nimport ActiveAccountPage from './pages/auth/ActiveAccountPage';\nimport Error401 from './pages/error/Error401';\nimport PageNotFound from './pages/error/PageNotFound';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\nimport '../src/assets/styles/custom.css';\nimport './App.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppRouter() {\n  _s();\n  const token = localStorage.getItem('token');\n  const role = localStorage.getItem('role');\n  const navigate = useNavigate();\n  const initialLoadRef = useRef(true);\n  useEffect(() => {\n    // Only redirect on initial load\n    if (initialLoadRef.current && token && window.location.pathname === '/') {\n      initialLoadRef.current = false;\n      if (role === 'trainee') {\n        navigate('/user/dashboard');\n      } else {\n        // For any other role (admin, trainer, etc.)\n        navigate('/admin/dashboard');\n      }\n    }\n  }, [token, role, navigate]);\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/public/courseDetails/:encodedId\",\n      element: /*#__PURE__*/_jsxDEV(PublicCourseDetails, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 63\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), !token && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 36\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/register\",\n        element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/forgot-password\",\n        element: /*#__PURE__*/_jsxDEV(ForgotPassword, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/forgot-password-otp\",\n        element: /*#__PURE__*/_jsxDEV(ForgotPasswordOTP, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 60\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/register-otp\",\n        element: /*#__PURE__*/_jsxDEV(RegisterOTP, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/reset-password\",\n        element: /*#__PURE__*/_jsxDEV(ResetPassword, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/active-account\",\n        element: /*#__PURE__*/_jsxDEV(ActiveAccountPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/error/401\",\n        element: /*#__PURE__*/_jsxDEV(Error401, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 45\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(PageNotFound, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 36\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), token && role === 'trainee' && /*#__PURE__*/_jsxDEV(Route, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(UserRoutes, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 64\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 39\n    }, this), token && role !== 'trainee' && /*#__PURE__*/_jsxDEV(Route, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(AdminRoutes, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 64\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 39\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n}\n_s(AppRouter, \"hfTeuykHMev6UMiB/hNK1PK1I3o=\", false, function () {\n  return [useNavigate];\n});\n_c = AppRouter;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(PermissionsProvider, {\n    children: /*#__PURE__*/_jsxDEV(NotificationProvider, {\n      children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        children: /*#__PURE__*/_jsxDEV(AppRouter, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppRouter\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Navigate", "useNavigate", "useEffect", "useRef", "UserRoutes", "AdminRoutes", "PublicCourseDetails", "NotificationProvider", "PermissionsProvider", "<PERSON><PERSON>", "Register", "RegisterOTP", "ForgotPassword", "ForgotPasswordOTP", "ResetPassword", "ActiveAccountPage", "Error401", "PageNotFound", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AppRouter", "_s", "token", "localStorage", "getItem", "role", "navigate", "initialLoadRef", "current", "window", "location", "pathname", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/App.js"], "sourcesContent": ["import { BrowserRouter, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\nimport { useEffect, useRef } from 'react';\n\nimport UserRoutes from './routes/UserRoutes.jsx';\nimport AdminRoutes from './routes/AdminRoutes.jsx';\nimport PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';\nimport { NotificationProvider } from './context/NotificationContext';\nimport { PermissionsProvider } from './context/PermissionsContext';\n\n// Import auth pages\nimport Login from './pages/auth/Login';\nimport Register from './pages/auth/Register';\nimport RegisterOTP from './pages/auth/RegisterOTP';\nimport ForgotPassword from './pages/auth/ForgotPassword';\nimport ForgotPasswordOTP from './pages/auth/ForgotPasswordOTP';\nimport ResetPassword from './pages/auth/ResetPassword';\nimport ActiveAccountPage from './pages/auth/ActiveAccountPage';\nimport Error401 from './pages/error/Error401';\nimport PageNotFound from './pages/error/PageNotFound';\n\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\nimport '../src/assets/styles/custom.css';\nimport './App.css';\n\nfunction AppRouter() {\n  const token = localStorage.getItem('token');\n  const role = localStorage.getItem('role');\n  const navigate = useNavigate();\n  const initialLoadRef = useRef(true);\n  \n  useEffect(() => {\n    // Only redirect on initial load\n    if (initialLoadRef.current && token && window.location.pathname === '/') {\n      initialLoadRef.current = false;\n      if (role === 'trainee') {\n        navigate('/user/dashboard');\n      } else {\n        // For any other role (admin, trainer, etc.)\n        navigate('/admin/dashboard');\n      }\n    }\n  }, [token, role, navigate]);\n\n  return (\n    <Routes>\n      {/* Public routes - accessible without authentication */}\n      <Route path=\"/public/courseDetails/:encodedId\" element={<PublicCourseDetails />} />\n\n      {/* Auth routes - when not authenticated */}\n      {!token && (\n        <>\n          <Route path=\"/\" element={<Login />} />\n          <Route path=\"/auth/login\" element={<Login />} />\n          <Route path=\"/auth/register\" element={<Register />} />\n          <Route path=\"/auth/forgot-password\" element={<ForgotPassword />} />\n          <Route path=\"/auth/forgot-password-otp\" element={<ForgotPasswordOTP />} />\n          <Route path=\"/auth/register-otp\" element={<RegisterOTP />} />\n          <Route path=\"/auth/reset-password\" element={<ResetPassword />} />\n          <Route path=\"/auth/active-account\" element={<ActiveAccountPage />} />\n          <Route path=\"/error/401\" element={<Error401 />} />\n          <Route path=\"*\" element={<PageNotFound />} />\n        </>\n      )}\n\n      {/* User routes - when authenticated as trainee */}\n      {token && role === 'trainee' && <Route path=\"*\" element={<UserRoutes />} />}\n\n      {/* Admin routes - when authenticated as admin/trainer */}\n      {token && role !== 'trainee' && <Route path=\"*\" element={<AdminRoutes />} />}\n    </Routes>\n  );\n}\n\nfunction App() {\n  return (\n    <PermissionsProvider>\n    <NotificationProvider>\n      <BrowserRouter>\n        <AppRouter />\n      </BrowserRouter>\n    </NotificationProvider>\n    </PermissionsProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACtF,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEzC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,mBAAmB,MAAM,wCAAwC;AACxE,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,mBAAmB,QAAQ,8BAA8B;;AAElE;AACA,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,YAAY,MAAM,4BAA4B;AAErD,OAAO,sCAAsC;AAC7C,OAAO,2CAA2C;AAClD,OAAO,iCAAiC;AACxC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,MAAMC,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EACzC,MAAME,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,cAAc,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACd;IACA,IAAI2B,cAAc,CAACC,OAAO,IAAIN,KAAK,IAAIO,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,GAAG,EAAE;MACvEJ,cAAc,CAACC,OAAO,GAAG,KAAK;MAC9B,IAAIH,IAAI,KAAK,SAAS,EAAE;QACtBC,QAAQ,CAAC,iBAAiB,CAAC;MAC7B,CAAC,MAAM;QACL;QACAA,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;IACF;EACF,CAAC,EAAE,CAACJ,KAAK,EAAEG,IAAI,EAAEC,QAAQ,CAAC,CAAC;EAE3B,oBACET,OAAA,CAACrB,MAAM;IAAAoC,QAAA,gBAELf,OAAA,CAACpB,KAAK;MAACoC,IAAI,EAAC,kCAAkC;MAACC,OAAO,eAAEjB,OAAA,CAACb,mBAAmB;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGlF,CAAChB,KAAK,iBACLL,OAAA,CAAAE,SAAA;MAAAa,QAAA,gBACEf,OAAA,CAACpB,KAAK;QAACoC,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEjB,OAAA,CAACV,KAAK;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtCrB,OAAA,CAACpB,KAAK;QAACoC,IAAI,EAAC,aAAa;QAACC,OAAO,eAAEjB,OAAA,CAACV,KAAK;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChDrB,OAAA,CAACpB,KAAK;QAACoC,IAAI,EAAC,gBAAgB;QAACC,OAAO,eAAEjB,OAAA,CAACT,QAAQ;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDrB,OAAA,CAACpB,KAAK;QAACoC,IAAI,EAAC,uBAAuB;QAACC,OAAO,eAAEjB,OAAA,CAACP,cAAc;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnErB,OAAA,CAACpB,KAAK;QAACoC,IAAI,EAAC,2BAA2B;QAACC,OAAO,eAAEjB,OAAA,CAACN,iBAAiB;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1ErB,OAAA,CAACpB,KAAK;QAACoC,IAAI,EAAC,oBAAoB;QAACC,OAAO,eAAEjB,OAAA,CAACR,WAAW;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DrB,OAAA,CAACpB,KAAK;QAACoC,IAAI,EAAC,sBAAsB;QAACC,OAAO,eAAEjB,OAAA,CAACL,aAAa;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjErB,OAAA,CAACpB,KAAK;QAACoC,IAAI,EAAC,sBAAsB;QAACC,OAAO,eAAEjB,OAAA,CAACJ,iBAAiB;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrErB,OAAA,CAACpB,KAAK;QAACoC,IAAI,EAAC,YAAY;QAACC,OAAO,eAAEjB,OAAA,CAACH,QAAQ;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDrB,OAAA,CAACpB,KAAK;QAACoC,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEjB,OAAA,CAACF,YAAY;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA,eAC7C,CACH,EAGAhB,KAAK,IAAIG,IAAI,KAAK,SAAS,iBAAIR,OAAA,CAACpB,KAAK;MAACoC,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEjB,OAAA,CAACf,UAAU;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAG1EhB,KAAK,IAAIG,IAAI,KAAK,SAAS,iBAAIR,OAAA,CAACpB,KAAK;MAACoC,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEjB,OAAA,CAACd,WAAW;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtE,CAAC;AAEb;AAACjB,EAAA,CA/CQD,SAAS;EAAA,QAGCrB,WAAW;AAAA;AAAAwC,EAAA,GAHrBnB,SAAS;AAiDlB,SAASoB,GAAGA,CAAA,EAAG;EACb,oBACEvB,OAAA,CAACX,mBAAmB;IAAA0B,QAAA,eACpBf,OAAA,CAACZ,oBAAoB;MAAA2B,QAAA,eACnBf,OAAA,CAACtB,aAAa;QAAAqC,QAAA,eACZf,OAAA,CAACG,SAAS;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAE1B;AAACG,GAAA,GAVQD,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}