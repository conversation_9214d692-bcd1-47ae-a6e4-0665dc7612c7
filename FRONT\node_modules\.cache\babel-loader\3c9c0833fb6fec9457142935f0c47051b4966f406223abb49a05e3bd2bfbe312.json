{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\auth\\\\SingaporeCallback.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useNavigate, useSearchParams } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { getLogoByDomainAndAlt } from '../../utils/logoUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SingaporeCallback() {\n  _s();\n  const [searchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const [status, setStatus] = useState('processing'); // processing, success, error\n  const [message, setMessage] = useState('Processing Singapore validation...');\n  useEffect(() => {\n    const handleCallback = () => {\n      // Check for success parameter\n      const successMessage = searchParams.get('singapore_success');\n      if (successMessage) {\n        setStatus('success');\n        setMessage('Singapore validation completed successfully!');\n        toast.success(successMessage, {\n          position: \"top-center\",\n          autoClose: 3000,\n          hideProgressBar: false,\n          closeOnClick: true,\n          pauseOnHover: true,\n          draggable: true,\n          onClose: () => {\n            navigate('/auth/login');\n          }\n        });\n\n        // Auto redirect after 3 seconds\n        setTimeout(() => {\n          navigate('/auth/login');\n        }, 3000);\n        return;\n      }\n\n      // Check for error parameter\n      const errorMessage = searchParams.get('singapore_error');\n      if (errorMessage) {\n        setStatus('error');\n        setMessage(`Singapore validation failed: ${errorMessage}`);\n        toast.error(`Singapore validation failed: ${errorMessage}`, {\n          position: \"top-center\",\n          autoClose: 5000,\n          hideProgressBar: false,\n          closeOnClick: true,\n          pauseOnHover: true,\n          draggable: true\n        });\n        return;\n      }\n\n      // If no parameters, something went wrong\n      setStatus('error');\n      setMessage('Invalid callback - no status received');\n      toast.error('Invalid callback - no status received', {\n        position: \"top-center\",\n        autoClose: 3000,\n        hideProgressBar: false,\n        closeOnClick: true,\n        pauseOnHover: true,\n        draggable: true\n      });\n    };\n    handleCallback();\n  }, [searchParams, navigate]);\n  const handleReturnToLogin = () => {\n    navigate('/auth/login');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full bg-white rounded-2xl shadow-xl p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: getLogoByDomainAndAlt(window.location.origin, '/images/logo.png'),\n          alt: \"Logo\",\n          className: \"h-12 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Singapore Validation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [status === 'processing' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this), status === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-green-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M5 13l4 4L19 7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-green-600\",\n            children: \"Validation Successful!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Redirecting to login page...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this), status === 'error' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-red-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-red-600\",\n            children: \"Validation Failed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 text-sm\",\n            children: message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleReturnToLogin,\n              className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-200\",\n              children: \"Return to Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500 space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"If you continue to experience issues:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"list-disc list-inside text-left space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Ensure your IC/FIN number is correctly entered in your profile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Make sure you're using the same IC/FIN registered with Singapore government\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Contact support if the problem persists\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 pt-6 border-t border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center space-x-2 text-sm text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: \"2\",\n              d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Secured by Singapore MyInfo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n}\n_s(SingaporeCallback, \"pJGRet/0L2pOAlop2FYqC+qnkAY=\", false, function () {\n  return [useSearchParams, useNavigate];\n});\n_c = SingaporeCallback;\nexport default SingaporeCallback;\nvar _c;\n$RefreshReg$(_c, \"SingaporeCallback\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useNavigate", "useSearchParams", "toast", "getLogoByDomainAndAlt", "jsxDEV", "_jsxDEV", "SingaporeCallback", "_s", "searchParams", "navigate", "status", "setStatus", "message", "setMessage", "handleCallback", "successMessage", "get", "success", "position", "autoClose", "hideProgressBar", "closeOnClick", "pauseOnHover", "draggable", "onClose", "setTimeout", "errorMessage", "error", "handleReturnToLogin", "className", "children", "src", "window", "location", "origin", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/auth/SingaporeCallback.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useNavigate, useSearchParams } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { getLogoByDomainAndAlt } from '../../utils/logoUtils';\n\nfunction SingaporeCallback() {\n  const [searchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const [status, setStatus] = useState('processing'); // processing, success, error\n  const [message, setMessage] = useState('Processing Singapore validation...');\n\n  useEffect(() => {\n    const handleCallback = () => {\n      // Check for success parameter\n      const successMessage = searchParams.get('singapore_success');\n      if (successMessage) {\n        setStatus('success');\n        setMessage('Singapore validation completed successfully!');\n        \n        toast.success(successMessage, {\n          position: \"top-center\",\n          autoClose: 3000,\n          hideProgressBar: false,\n          closeOnClick: true,\n          pauseOnHover: true,\n          draggable: true,\n          onClose: () => {\n            navigate('/auth/login');\n          }\n        });\n\n        // Auto redirect after 3 seconds\n        setTimeout(() => {\n          navigate('/auth/login');\n        }, 3000);\n        \n        return;\n      }\n\n      // Check for error parameter\n      const errorMessage = searchParams.get('singapore_error');\n      if (errorMessage) {\n        setStatus('error');\n        setMessage(`Singapore validation failed: ${errorMessage}`);\n        \n        toast.error(`Singapore validation failed: ${errorMessage}`, {\n          position: \"top-center\",\n          autoClose: 5000,\n          hideProgressBar: false,\n          closeOnClick: true,\n          pauseOnHover: true,\n          draggable: true\n        });\n\n        return;\n      }\n\n      // If no parameters, something went wrong\n      setStatus('error');\n      setMessage('Invalid callback - no status received');\n      \n      toast.error('Invalid callback - no status received', {\n        position: \"top-center\",\n        autoClose: 3000,\n        hideProgressBar: false,\n        closeOnClick: true,\n        pauseOnHover: true,\n        draggable: true\n      });\n    };\n\n    handleCallback();\n  }, [searchParams, navigate]);\n\n  const handleReturnToLogin = () => {\n    navigate('/auth/login');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full bg-white rounded-2xl shadow-xl p-8\">\n        {/* Logo */}\n        <div className=\"text-center mb-8\">\n          <img \n            src={getLogoByDomainAndAlt(window.location.origin, '/images/logo.png')} \n            alt=\"Logo\" \n            className=\"h-12 mx-auto mb-4\"\n          />\n          <h1 className=\"text-2xl font-bold text-gray-900\">Singapore Validation</h1>\n        </div>\n\n        {/* Status Content */}\n        <div className=\"text-center\">\n          {status === 'processing' && (\n            <div className=\"space-y-4\">\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n              <p className=\"text-gray-600\">{message}</p>\n            </div>\n          )}\n\n          {status === 'success' && (\n            <div className=\"space-y-4\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\"></path>\n                </svg>\n              </div>\n              <h2 className=\"text-xl font-semibold text-green-600\">Validation Successful!</h2>\n              <p className=\"text-gray-600\">{message}</p>\n              <p className=\"text-sm text-gray-500\">Redirecting to login page...</p>\n            </div>\n          )}\n\n          {status === 'error' && (\n            <div className=\"space-y-4\">\n              <div className=\"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto\">\n                <svg className=\"w-6 h-6 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                </svg>\n              </div>\n              <h2 className=\"text-xl font-semibold text-red-600\">Validation Failed</h2>\n              <p className=\"text-gray-600 text-sm\">{message}</p>\n              \n              <div className=\"space-y-3 mt-6\">\n                <button\n                  onClick={handleReturnToLogin}\n                  className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-200\"\n                >\n                  Return to Login\n                </button>\n                \n                <div className=\"text-xs text-gray-500 space-y-1\">\n                  <p>If you continue to experience issues:</p>\n                  <ul className=\"list-disc list-inside text-left space-y-1\">\n                    <li>Ensure your IC/FIN number is correctly entered in your profile</li>\n                    <li>Make sure you're using the same IC/FIN registered with Singapore government</li>\n                    <li>Contact support if the problem persists</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Additional Info */}\n        <div className=\"mt-8 pt-6 border-t border-gray-200\">\n          <div className=\"flex items-center justify-center space-x-2 text-sm text-gray-500\">\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"></path>\n            </svg>\n            <span>Secured by Singapore MyInfo</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default SingaporeCallback;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC/D,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,qBAAqB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,SAASC,iBAAiBA,CAAA,EAAG;EAAAC,EAAA;EAC3B,MAAM,CAACC,YAAY,CAAC,GAAGP,eAAe,CAAC,CAAC;EACxC,MAAMQ,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,MAAM,EAAEC,SAAS,CAAC,GAAGZ,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;EACpD,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,oCAAoC,CAAC;EAE5ED,SAAS,CAAC,MAAM;IACd,MAAMgB,cAAc,GAAGA,CAAA,KAAM;MAC3B;MACA,MAAMC,cAAc,GAAGP,YAAY,CAACQ,GAAG,CAAC,mBAAmB,CAAC;MAC5D,IAAID,cAAc,EAAE;QAClBJ,SAAS,CAAC,SAAS,CAAC;QACpBE,UAAU,CAAC,8CAA8C,CAAC;QAE1DX,KAAK,CAACe,OAAO,CAACF,cAAc,EAAE;UAC5BG,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAE,IAAI;UACfC,eAAe,EAAE,KAAK;UACtBC,YAAY,EAAE,IAAI;UAClBC,YAAY,EAAE,IAAI;UAClBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAEA,CAAA,KAAM;YACbf,QAAQ,CAAC,aAAa,CAAC;UACzB;QACF,CAAC,CAAC;;QAEF;QACAgB,UAAU,CAAC,MAAM;UACfhB,QAAQ,CAAC,aAAa,CAAC;QACzB,CAAC,EAAE,IAAI,CAAC;QAER;MACF;;MAEA;MACA,MAAMiB,YAAY,GAAGlB,YAAY,CAACQ,GAAG,CAAC,iBAAiB,CAAC;MACxD,IAAIU,YAAY,EAAE;QAChBf,SAAS,CAAC,OAAO,CAAC;QAClBE,UAAU,CAAC,gCAAgCa,YAAY,EAAE,CAAC;QAE1DxB,KAAK,CAACyB,KAAK,CAAC,gCAAgCD,YAAY,EAAE,EAAE;UAC1DR,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAE,IAAI;UACfC,eAAe,EAAE,KAAK;UACtBC,YAAY,EAAE,IAAI;UAClBC,YAAY,EAAE,IAAI;UAClBC,SAAS,EAAE;QACb,CAAC,CAAC;QAEF;MACF;;MAEA;MACAZ,SAAS,CAAC,OAAO,CAAC;MAClBE,UAAU,CAAC,uCAAuC,CAAC;MAEnDX,KAAK,CAACyB,KAAK,CAAC,uCAAuC,EAAE;QACnDT,QAAQ,EAAE,YAAY;QACtBC,SAAS,EAAE,IAAI;QACfC,eAAe,EAAE,KAAK;QACtBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,IAAI;QAClBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC;IAEDT,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACN,YAAY,EAAEC,QAAQ,CAAC,CAAC;EAE5B,MAAMmB,mBAAmB,GAAGA,CAAA,KAAM;IAChCnB,QAAQ,CAAC,aAAa,CAAC;EACzB,CAAC;EAED,oBACEJ,OAAA;IAAKwB,SAAS,EAAC,gGAAgG;IAAAC,QAAA,eAC7GzB,OAAA;MAAKwB,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBAEjEzB,OAAA;QAAKwB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BzB,OAAA;UACE0B,GAAG,EAAE5B,qBAAqB,CAAC6B,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE,kBAAkB,CAAE;UACvEC,GAAG,EAAC,MAAM;UACVN,SAAS,EAAC;QAAmB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACFlC,OAAA;UAAIwB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAoB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eAGNlC,OAAA;QAAKwB,SAAS,EAAC,aAAa;QAAAC,QAAA,GACzBpB,MAAM,KAAK,YAAY,iBACtBL,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBzB,OAAA;YAAKwB,SAAS,EAAC;UAAwE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9FlC,OAAA;YAAGwB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAElB;UAAO;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN,EAEA7B,MAAM,KAAK,SAAS,iBACnBL,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBzB,OAAA;YAAKwB,SAAS,EAAC,8EAA8E;YAAAC,QAAA,eAC3FzB,OAAA;cAAKwB,SAAS,EAAC,wBAAwB;cAACW,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAZ,QAAA,eAC3FzB,OAAA;gBAAMsC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAC,GAAG;gBAACC,CAAC,EAAC;cAAgB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlC,OAAA;YAAIwB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAsB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFlC,OAAA;YAAGwB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAElB;UAAO;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1ClC,OAAA;YAAGwB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAA4B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CACN,EAEA7B,MAAM,KAAK,OAAO,iBACjBL,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBzB,OAAA;YAAKwB,SAAS,EAAC,4EAA4E;YAAAC,QAAA,eACzFzB,OAAA;cAAKwB,SAAS,EAAC,sBAAsB;cAACW,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAZ,QAAA,eACzFzB,OAAA;gBAAMsC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAC,GAAG;gBAACC,CAAC,EAAC;cAAsB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlC,OAAA;YAAIwB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAiB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzElC,OAAA;YAAGwB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAElB;UAAO;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAElDlC,OAAA;YAAKwB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzB,OAAA;cACE0C,OAAO,EAAEnB,mBAAoB;cAC7BC,SAAS,EAAC,8FAA8F;cAAAC,QAAA,EACzG;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETlC,OAAA;cAAKwB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9CzB,OAAA;gBAAAyB,QAAA,EAAG;cAAqC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5ClC,OAAA;gBAAIwB,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,gBACvDzB,OAAA;kBAAAyB,QAAA,EAAI;gBAA8D;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvElC,OAAA;kBAAAyB,QAAA,EAAI;gBAA2E;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpFlC,OAAA;kBAAAyB,QAAA,EAAI;gBAAuC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNlC,OAAA;QAAKwB,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDzB,OAAA;UAAKwB,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/EzB,OAAA;YAAKwB,SAAS,EAAC,SAAS;YAACW,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAZ,QAAA,eAC5EzB,OAAA;cAAMsC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAC,GAAG;cAACC,CAAC,EAAC;YAAsG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChL,CAAC,eACNlC,OAAA;YAAAyB,QAAA,EAAM;UAA2B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAChC,EAAA,CAvJQD,iBAAiB;EAAA,QACDL,eAAe,EACrBD,WAAW;AAAA;AAAAgD,EAAA,GAFrB1C,iBAAiB;AAyJ1B,eAAeA,iBAAiB;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}