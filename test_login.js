const axios = require('axios');

// Test login without Singpass verification
async function testLogin() {
  try {
    console.log('🧪 Testing login without Singpass verification...');
    
    const loginData = {
      email: '<EMAIL>', // Replace with a valid test email
      password: 'testpassword123', // Replace with a valid test password
      url: 'http://test.localhost:3001'
    };

    const response = await axios.post('http://localhost:5001/org/user_login', loginData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Login successful!');
    console.log('Response:', response.data);
    
  } catch (error) {
    if (error.response) {
      console.log('❌ Login failed with status:', error.response.status);
      console.log('Error message:', error.response.data);
    } else {
      console.log('❌ Network error:', error.message);
    }
  }
}

// Run the test
testLogin();
