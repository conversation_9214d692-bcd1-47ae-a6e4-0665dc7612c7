{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\auth\\\\Login.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useNavigate } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { validateEmail, validatePassword, validateForm } from '../../utils/validation';\nimport { getLogoByDomainAndAlt } from '../../utils/logoUtils';\nimport { loginApi, sendOTPApi, ActiveAccountOTPFromLoginPage } from '../../services/authService';\nimport { usePermissions } from '../../context/PermissionsContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Login() {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const navigate = useNavigate();\n  const {\n    setPermissionsFromAPI\n  } = usePermissions();\n\n  // Validation Rules\n  const validationRules = {\n    email: validateEmail,\n    password: validatePassword\n  };\n\n  // Handle Change\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Update both form data and clear errors in a single function\n    const newFormData = {\n      ...formData,\n      [name]: value\n    };\n    const newErrors = {\n      ...errors,\n      [name]: undefined\n    };\n    setFormData(newFormData);\n    setErrors(newErrors);\n  };\n\n  // Handle Submit\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const validation = validateForm(formData, validationRules);\n    if (validation.isValid) {\n      setIsLoading(true);\n      const payload = {\n        email: formData.email,\n        password: formData.password,\n        organization_url: window.location.origin\n      };\n      console.log(\"Payload being sent to API:-------------------------\", payload);\n      try {\n        console.log(\"Payload being sent to API:\", payload);\n        const response = await loginApi(payload);\n        console.log(\"API response:\", response);\n\n        // ⛔ Singapore validation required\n        if (response !== null && response !== void 0 && response.singapore_validation_required) {\n          console.log(\"Singapore validation required, redirecting to MyInfo\");\n          toast.info('Singapore validation required. Redirecting to MyInfo...', {\n            position: \"top-center\",\n            autoClose: 2000,\n            hideProgressBar: false,\n            closeOnClick: true,\n            pauseOnHover: true,\n            draggable: true,\n            onClose: () => {\n              // Redirect to Singapore MyInfo authorization\n              window.location.href = response.singapore_auth_url;\n            }\n          });\n          setIsLoading(false);\n          return;\n        }\n\n        // ⛔ Not a Singapore user\n        if ((response === null || response === void 0 ? void 0 : response.is_singapore_user) === false) {\n          console.log(\"User is not a Singapore user\");\n          toast.error('You are not a Singapore user. This system is only for Singapore residents.', {\n            position: \"top-center\",\n            autoClose: 5000,\n            hideProgressBar: false,\n            closeOnClick: true,\n            pauseOnHover: true,\n            draggable: true\n          });\n          setIsLoading(false);\n          return;\n        }\n\n        // ⛔ Email not verified - redirect to OTP page\n        if ((response === null || response === void 0 ? void 0 : response.is_email_verified) === 0) {\n          console.log(\"Email not verified, sending OTP and redirecting to activation page\");\n          try {\n            // Call sendOTP API to send activation OTP\n            const otpPayload = {\n              email: formData.email,\n              domain: window.location.origin\n            };\n            await ActiveAccountOTPFromLoginPage(otpPayload);\n            console.log(\"OTP sent successfully for account activation\");\n            toast.success('Account registered but not activated. Please check your email.', {\n              position: \"top-center\",\n              autoClose: 2000,\n              hideProgressBar: false,\n              closeOnClick: true,\n              pauseOnHover: true,\n              draggable: true,\n              onClose: () => {\n                setIsLoading(false); // Turn off loader only when redirecting\n                navigate('/auth/active-account', {\n                  state: {\n                    email: formData.email\n                  }\n                });\n              }\n            });\n          } catch (otpError) {\n            console.error(\"Error sending OTP:\", otpError);\n            toast.error('Failed to send OTP. Please try again.', {\n              position: \"top-center\",\n              autoClose: 3000\n            });\n            setIsLoading(false);\n          }\n          return;\n        }\n\n        // Always wait 500ms before showing result\n        await new Promise(resolve => setTimeout(resolve, 500));\n        if (response.success) {\n          // Set permissions in context\n          setPermissionsFromAPI(response.data.permissions);\n          localStorage.setItem('token', response.data.access_token);\n          localStorage.setItem('refresh_token', response.data.refresh_token);\n          localStorage.setItem('user', JSON.stringify(response.data.user_details));\n          localStorage.setItem('organization_details', JSON.stringify(response.data.dashboard));\n          localStorage.setItem('role', response.data.role);\n          toast.success(response.data.message || 'Login successful', {\n            position: \"top-center\",\n            autoClose: 1000,\n            onClose: () => {\n              console.log(\"Role:\", response.data.role);\n              if (response.data.role === 'trainee') {\n                navigate('/user/dashboard');\n              } else {\n                navigate('/admin/dashboard');\n              }\n            }\n          });\n        } else {\n          toast.error(response.message || \"Login failed\", {\n            position: \"top-center\",\n            autoClose: 5000\n          });\n          setIsLoading(false);\n        }\n      } catch (error) {\n        var _error$response, _error$response2, _error$response2$data;\n        console.error(\"Login API error:\", error === null || error === void 0 ? void 0 : (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n        await new Promise(resolve => setTimeout(resolve, 500));\n        toast.error((error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Something went wrong\", {\n          position: \"top-center\",\n          autoClose: 5000\n        });\n        setIsLoading(false);\n      }\n    } else {\n      setErrors(validation.errors);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-logo\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: getLogoByDomainAndAlt().logo,\n        alt: getLogoByDomainAndAlt().alt,\n        style: {\n          width: '180px',\n          height: 'auto'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Login to LMS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Enter your email & password to login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"auth-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            children: \"Email Address\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"mdi:email-outline\",\n              className: \"auth-input-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleChange,\n              placeholder: \"<EMAIL>\",\n              className: `auth-input ${errors.email ? 'error' : ''}`,\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"auth-error-message\",\n            children: errors.email[0]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 30\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"mdi:lock-outline\",\n              className: \"auth-input-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: showPassword ? \"text\" : \"password\",\n              id: \"password\",\n              name: \"password\",\n              value: formData.password,\n              onChange: handleChange,\n              placeholder: \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\",\n              className: `auth-input ${errors.password ? 'error' : ''}`,\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"auth-password-toggle\",\n              onClick: () => setShowPassword(!showPassword),\n              \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n              disabled: isLoading,\n              children: /*#__PURE__*/_jsxDEV(Icon, {\n                icon: showPassword ? \"mdi:eye-off-outline\" : \"mdi:eye-outline\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"auth-error-message\",\n            children: errors.password[0]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-forgot-password\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/auth/forgot-password\",\n            className: isLoading ? 'disabled-link' : '',\n            children: \"Forgot Password?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: `btn btn-primary ${isLoading ? 'loading' : ''}`,\n          disabled: isLoading,\n          children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), \"Logging in...\"]\n          }, void 0, true) : 'Login'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-options\",\n          children: [\"Don't have an account? \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/auth/register\",\n            className: isLoading ? 'disabled-link' : '',\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 36\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"ElsfJdFk2Lcq3fBUMq+pb+kAB3k=\", false, function () {\n  return [useNavigate, usePermissions];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Icon", "useNavigate", "toast", "validateEmail", "validatePassword", "validateForm", "getLogoByDomainAndAlt", "loginApi", "sendOTPApi", "ActiveAccountOTPFromLoginPage", "usePermissions", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "formData", "setFormData", "email", "password", "errors", "setErrors", "showPassword", "setShowPassword", "isLoading", "setIsLoading", "navigate", "setPermissionsFromAPI", "validationRules", "handleChange", "e", "name", "value", "target", "newFormData", "newErrors", "undefined", "handleSubmit", "preventDefault", "validation", "<PERSON><PERSON><PERSON><PERSON>", "payload", "organization_url", "window", "location", "origin", "console", "log", "response", "singapore_validation_required", "info", "position", "autoClose", "hideProgressBar", "closeOnClick", "pauseOnHover", "draggable", "onClose", "href", "singapore_auth_url", "is_singapore_user", "error", "is_email_verified", "otpPayload", "domain", "success", "state", "otpError", "Promise", "resolve", "setTimeout", "data", "permissions", "localStorage", "setItem", "access_token", "refresh_token", "JSON", "stringify", "user_details", "dashboard", "role", "message", "_error$response", "_error$response2", "_error$response2$data", "className", "children", "src", "logo", "alt", "style", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "icon", "type", "id", "onChange", "placeholder", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/auth/Login.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Icon } from '@iconify/react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { toast } from 'react-toastify';\r\nimport { validateEmail, validatePassword, validateForm } from '../../utils/validation';\r\nimport { getLogoByDomainAndAlt } from '../../utils/logoUtils';\r\nimport { loginApi, sendOTPApi, ActiveAccountOTPFromLoginPage } from '../../services/authService';\r\nimport { usePermissions } from '../../context/PermissionsContext';\r\n\r\n\r\nfunction Login() {\r\n  const [formData, setFormData] = useState({\r\n    email: '',\r\n    password: ''\r\n  });\r\n  const [errors, setErrors] = useState({});\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const navigate = useNavigate();\r\n  const { setPermissionsFromAPI } = usePermissions();\r\n\r\n  // Validation Rules\r\n  const validationRules = {\r\n    email: validateEmail,\r\n    password: validatePassword\r\n  };\r\n\r\n  // Handle Change\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    \r\n    // Update both form data and clear errors in a single function\r\n    const newFormData = { ...formData, [name]: value };\r\n    const newErrors = { ...errors, [name]: undefined };\r\n    \r\n    setFormData(newFormData);\r\n    setErrors(newErrors);\r\n  };\r\n\r\n  // Handle Submit\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n  \r\n    const validation = validateForm(formData, validationRules);\r\n    if (validation.isValid) {\r\n      setIsLoading(true);\r\n  \r\n      const payload = {\r\n        email: formData.email,\r\n        password: formData.password,\r\n        organization_url: window.location.origin\r\n      };\r\n  \r\n      console.log(\"Payload being sent to API:-------------------------\", payload);\r\n\r\n      try {\r\n        console.log(\"Payload being sent to API:\", payload);\r\n  \r\n        const response = await loginApi(payload);\r\n        console.log(\"API response:\", response);\r\n  \r\n        // ⛔ Singapore validation required\r\n        if (response?.singapore_validation_required) {\r\n          console.log(\"Singapore validation required, redirecting to MyInfo\");\r\n\r\n          toast.info('Singapore validation required. Redirecting to MyInfo...', {\r\n            position: \"top-center\",\r\n            autoClose: 2000,\r\n            hideProgressBar: false,\r\n            closeOnClick: true,\r\n            pauseOnHover: true,\r\n            draggable: true,\r\n            onClose: () => {\r\n              // Redirect to Singapore MyInfo authorization\r\n              window.location.href = response.singapore_auth_url;\r\n            }\r\n          });\r\n\r\n          setIsLoading(false);\r\n          return;\r\n        }\r\n\r\n        // ⛔ Not a Singapore user\r\n        if (response?.is_singapore_user === false) {\r\n          console.log(\"User is not a Singapore user\");\r\n\r\n          toast.error('You are not a Singapore user. This system is only for Singapore residents.', {\r\n            position: \"top-center\",\r\n            autoClose: 5000,\r\n            hideProgressBar: false,\r\n            closeOnClick: true,\r\n            pauseOnHover: true,\r\n            draggable: true\r\n          });\r\n\r\n          setIsLoading(false);\r\n          return;\r\n        }\r\n\r\n        // ⛔ Email not verified - redirect to OTP page\r\n        if (response?.is_email_verified === 0) {\r\n          console.log(\"Email not verified, sending OTP and redirecting to activation page\");\r\n\r\n          try {\r\n            // Call sendOTP API to send activation OTP\r\n            const otpPayload = {\r\n              email: formData.email,\r\n              domain: window.location.origin\r\n            };\r\n\r\n            await ActiveAccountOTPFromLoginPage(otpPayload);\r\n            console.log(\"OTP sent successfully for account activation\");\r\n\r\n           toast.success('Account registered but not activated. Please check your email.', {\r\n              position: \"top-center\",\r\n              autoClose: 2000,\r\n              hideProgressBar: false,\r\n              closeOnClick: true,\r\n              pauseOnHover: true,\r\n              draggable: true,\r\n              onClose: () => {\r\n                setIsLoading(false); // Turn off loader only when redirecting\r\n                navigate('/auth/active-account', {\r\n                  state: {\r\n                    email: formData.email\r\n                  }\r\n                });\r\n              }\r\n            });\r\n\r\n            \r\n          } catch (otpError) {\r\n            console.error(\"Error sending OTP:\", otpError);\r\n            toast.error('Failed to send OTP. Please try again.', {\r\n              position: \"top-center\",\r\n              autoClose: 3000\r\n            });\r\n            setIsLoading(false);\r\n          }\r\n          return;\r\n        }\r\n  \r\n        // Always wait 500ms before showing result\r\n        await new Promise(resolve => setTimeout(resolve, 500));\r\n  \r\n        if (response.success) {\r\n          // Set permissions in context\r\n          setPermissionsFromAPI(response.data.permissions);\r\n  \r\n          localStorage.setItem('token', response.data.access_token);\r\n          localStorage.setItem('refresh_token', response.data.refresh_token);\r\n          localStorage.setItem('user', JSON.stringify(response.data.user_details));\r\n          localStorage.setItem('organization_details', JSON.stringify(response.data.dashboard));\r\n          localStorage.setItem('role', response.data.role);\r\n  \r\n          toast.success(response.data.message || 'Login successful', {\r\n            position: \"top-center\",\r\n            autoClose: 1000,\r\n            onClose: () => {\r\n              console.log(\"Role:\", response.data.role);\r\n              if (response.data.role === 'trainee') {\r\n                navigate('/user/dashboard');\r\n              } else {\r\n                navigate('/admin/dashboard');\r\n              }\r\n            }\r\n          });\r\n        } else {\r\n          toast.error(response.message || \"Login failed\", {\r\n            position: \"top-center\",\r\n            autoClose: 5000\r\n          });\r\n          setIsLoading(false);\r\n        }\r\n  \r\n      } catch (error) {\r\n        console.error(\"Login API error:\", error?.response?.data);\r\n  \r\n        await new Promise(resolve => setTimeout(resolve, 500));\r\n  \r\n        toast.error(error?.response?.data?.message || \"Something went wrong\", {\r\n          position: \"top-center\",\r\n          autoClose: 5000\r\n        });\r\n        setIsLoading(false);\r\n      }\r\n    } else {\r\n      setErrors(validation.errors);\r\n    }\r\n  };\r\n  \r\n  \r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      {/* Logo  */}\r\n      <div className=\"auth-logo\">\r\n        <img src={getLogoByDomainAndAlt().logo} alt={getLogoByDomainAndAlt().alt} style={{ width: '180px', height: 'auto' }} />\r\n      </div>\r\n      {/* Login Card */}\r\n      <div className=\"auth-card\">\r\n        <div className=\"auth-header\">\r\n          <h2>Login to LMS</h2>\r\n          <p>Enter your email & password to login</p>\r\n        </div>\r\n        \r\n        <form onSubmit={handleSubmit} className=\"auth-form\">\r\n          <div className=\"auth-form-group\">\r\n            <label htmlFor=\"email\">Email Address</label>\r\n            <div className=\"auth-input-wrapper\">\r\n              <Icon icon=\"mdi:email-outline\" className=\"auth-input-icon\" />\r\n              <input \r\n                type=\"text\"\r\n                id=\"email\" \r\n                name=\"email\"\r\n                value={formData.email}\r\n                onChange={handleChange}\r\n                placeholder=\"<EMAIL>\" \r\n                className={`auth-input ${errors.email ? 'error' : ''}`}\r\n                disabled={isLoading}\r\n              />\r\n            </div>\r\n            {errors.email && <span className=\"auth-error-message\">{errors.email[0]}</span>}\r\n          </div>\r\n          \r\n          <div className=\"auth-form-group\">\r\n            <label htmlFor=\"password\">Password</label>\r\n            <div className=\"auth-input-wrapper\">\r\n              <Icon icon=\"mdi:lock-outline\" className=\"auth-input-icon\" />\r\n              <input \r\n                type={showPassword ? \"text\" : \"password\"} \r\n                id=\"password\" \r\n                name=\"password\"\r\n                value={formData.password}\r\n                onChange={handleChange}\r\n                placeholder=\"••••••\" \r\n                className={`auth-input ${errors.password ? 'error' : ''}`}\r\n                disabled={isLoading}\r\n              />\r\n              <button \r\n                type=\"button\" \r\n                className=\"auth-password-toggle\" \r\n                onClick={() => setShowPassword(!showPassword)}\r\n                aria-label={showPassword ? \"Hide password\" : \"Show password\"}\r\n                disabled={isLoading}\r\n              >\r\n                <Icon icon={showPassword ? \"mdi:eye-off-outline\" : \"mdi:eye-outline\"} />\r\n              </button>\r\n            </div>\r\n            {errors.password && <span className=\"auth-error-message\">{errors.password[0]}</span>}\r\n          </div>\r\n          \r\n          <div className=\"auth-forgot-password\">\r\n            <a href=\"/auth/forgot-password\" className={isLoading ? 'disabled-link' : ''}>Forgot Password?</a>\r\n          </div>\r\n          \r\n          <button \r\n            type=\"submit\" \r\n            className={`btn btn-primary ${isLoading ? 'loading' : ''}`}\r\n            disabled={isLoading}\r\n          >\r\n            {isLoading ? (\r\n              <>\r\n                <div className=\"spinner\"></div>\r\n                Logging in...\r\n              </>\r\n            ) : (\r\n              'Login'\r\n            )}\r\n          </button>\r\n          \r\n          <div className=\"auth-options\">\r\n            Don't have an account? <a href=\"/auth/register\" className={isLoading ? 'disabled-link' : ''}>Create Account</a>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,wBAAwB;AACtF,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,QAAQ,EAAEC,UAAU,EAAEC,6BAA6B,QAAQ,4BAA4B;AAChG,SAASC,cAAc,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGlE,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IACvCoB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM4B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2B;EAAsB,CAAC,GAAGlB,cAAc,CAAC,CAAC;;EAElD;EACA,MAAMmB,eAAe,GAAG;IACtBV,KAAK,EAAEhB,aAAa;IACpBiB,QAAQ,EAAEhB;EACZ,CAAC;;EAED;EACA,MAAM0B,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA,MAAMC,WAAW,GAAG;MAAE,GAAGlB,QAAQ;MAAE,CAACe,IAAI,GAAGC;IAAM,CAAC;IAClD,MAAMG,SAAS,GAAG;MAAE,GAAGf,MAAM;MAAE,CAACW,IAAI,GAAGK;IAAU,CAAC;IAElDnB,WAAW,CAACiB,WAAW,CAAC;IACxBb,SAAS,CAACc,SAAS,CAAC;EACtB,CAAC;;EAED;EACA,MAAME,YAAY,GAAG,MAAOP,CAAC,IAAK;IAChCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAElB,MAAMC,UAAU,GAAGnC,YAAY,CAACY,QAAQ,EAAEY,eAAe,CAAC;IAC1D,IAAIW,UAAU,CAACC,OAAO,EAAE;MACtBf,YAAY,CAAC,IAAI,CAAC;MAElB,MAAMgB,OAAO,GAAG;QACdvB,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAC3BuB,gBAAgB,EAAEC,MAAM,CAACC,QAAQ,CAACC;MACpC,CAAC;MAEDC,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEN,OAAO,CAAC;MAE3E,IAAI;QACFK,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEN,OAAO,CAAC;QAElD,MAAMO,QAAQ,GAAG,MAAM1C,QAAQ,CAACmC,OAAO,CAAC;QACxCK,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,QAAQ,CAAC;;QAEtC;QACA,IAAIA,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,6BAA6B,EAAE;UAC3CH,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;UAEnE9C,KAAK,CAACiD,IAAI,CAAC,yDAAyD,EAAE;YACpEC,QAAQ,EAAE,YAAY;YACtBC,SAAS,EAAE,IAAI;YACfC,eAAe,EAAE,KAAK;YACtBC,YAAY,EAAE,IAAI;YAClBC,YAAY,EAAE,IAAI;YAClBC,SAAS,EAAE,IAAI;YACfC,OAAO,EAAEA,CAAA,KAAM;cACb;cACAd,MAAM,CAACC,QAAQ,CAACc,IAAI,GAAGV,QAAQ,CAACW,kBAAkB;YACpD;UACF,CAAC,CAAC;UAEFlC,YAAY,CAAC,KAAK,CAAC;UACnB;QACF;;QAEA;QACA,IAAI,CAAAuB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEY,iBAAiB,MAAK,KAAK,EAAE;UACzCd,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAE3C9C,KAAK,CAAC4D,KAAK,CAAC,4EAA4E,EAAE;YACxFV,QAAQ,EAAE,YAAY;YACtBC,SAAS,EAAE,IAAI;YACfC,eAAe,EAAE,KAAK;YACtBC,YAAY,EAAE,IAAI;YAClBC,YAAY,EAAE,IAAI;YAClBC,SAAS,EAAE;UACb,CAAC,CAAC;UAEF/B,YAAY,CAAC,KAAK,CAAC;UACnB;QACF;;QAEA;QACA,IAAI,CAAAuB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEc,iBAAiB,MAAK,CAAC,EAAE;UACrChB,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;UAEjF,IAAI;YACF;YACA,MAAMgB,UAAU,GAAG;cACjB7C,KAAK,EAAEF,QAAQ,CAACE,KAAK;cACrB8C,MAAM,EAAErB,MAAM,CAACC,QAAQ,CAACC;YAC1B,CAAC;YAED,MAAMrC,6BAA6B,CAACuD,UAAU,CAAC;YAC/CjB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;YAE5D9C,KAAK,CAACgE,OAAO,CAAC,gEAAgE,EAAE;cAC7Ed,QAAQ,EAAE,YAAY;cACtBC,SAAS,EAAE,IAAI;cACfC,eAAe,EAAE,KAAK;cACtBC,YAAY,EAAE,IAAI;cAClBC,YAAY,EAAE,IAAI;cAClBC,SAAS,EAAE,IAAI;cACfC,OAAO,EAAEA,CAAA,KAAM;gBACbhC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;gBACrBC,QAAQ,CAAC,sBAAsB,EAAE;kBAC/BwC,KAAK,EAAE;oBACLhD,KAAK,EAAEF,QAAQ,CAACE;kBAClB;gBACF,CAAC,CAAC;cACJ;YACF,CAAC,CAAC;UAGJ,CAAC,CAAC,OAAOiD,QAAQ,EAAE;YACjBrB,OAAO,CAACe,KAAK,CAAC,oBAAoB,EAAEM,QAAQ,CAAC;YAC7ClE,KAAK,CAAC4D,KAAK,CAAC,uCAAuC,EAAE;cACnDV,QAAQ,EAAE,YAAY;cACtBC,SAAS,EAAE;YACb,CAAC,CAAC;YACF3B,YAAY,CAAC,KAAK,CAAC;UACrB;UACA;QACF;;QAEA;QACA,MAAM,IAAI2C,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;QAEtD,IAAIrB,QAAQ,CAACiB,OAAO,EAAE;UACpB;UACAtC,qBAAqB,CAACqB,QAAQ,CAACuB,IAAI,CAACC,WAAW,CAAC;UAEhDC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE1B,QAAQ,CAACuB,IAAI,CAACI,YAAY,CAAC;UACzDF,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE1B,QAAQ,CAACuB,IAAI,CAACK,aAAa,CAAC;UAClEH,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEG,IAAI,CAACC,SAAS,CAAC9B,QAAQ,CAACuB,IAAI,CAACQ,YAAY,CAAC,CAAC;UACxEN,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAEG,IAAI,CAACC,SAAS,CAAC9B,QAAQ,CAACuB,IAAI,CAACS,SAAS,CAAC,CAAC;UACrFP,YAAY,CAACC,OAAO,CAAC,MAAM,EAAE1B,QAAQ,CAACuB,IAAI,CAACU,IAAI,CAAC;UAEhDhF,KAAK,CAACgE,OAAO,CAACjB,QAAQ,CAACuB,IAAI,CAACW,OAAO,IAAI,kBAAkB,EAAE;YACzD/B,QAAQ,EAAE,YAAY;YACtBC,SAAS,EAAE,IAAI;YACfK,OAAO,EAAEA,CAAA,KAAM;cACbX,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEC,QAAQ,CAACuB,IAAI,CAACU,IAAI,CAAC;cACxC,IAAIjC,QAAQ,CAACuB,IAAI,CAACU,IAAI,KAAK,SAAS,EAAE;gBACpCvD,QAAQ,CAAC,iBAAiB,CAAC;cAC7B,CAAC,MAAM;gBACLA,QAAQ,CAAC,kBAAkB,CAAC;cAC9B;YACF;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACLzB,KAAK,CAAC4D,KAAK,CAACb,QAAQ,CAACkC,OAAO,IAAI,cAAc,EAAE;YAC9C/B,QAAQ,EAAE,YAAY;YACtBC,SAAS,EAAE;UACb,CAAC,CAAC;UACF3B,YAAY,CAAC,KAAK,CAAC;QACrB;MAEF,CAAC,CAAC,OAAOoC,KAAK,EAAE;QAAA,IAAAsB,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;QACdvC,OAAO,CAACe,KAAK,CAAC,kBAAkB,EAAEA,KAAK,aAALA,KAAK,wBAAAsB,eAAA,GAALtB,KAAK,CAAEb,QAAQ,cAAAmC,eAAA,uBAAfA,eAAA,CAAiBZ,IAAI,CAAC;QAExD,MAAM,IAAIH,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;QAEtDpE,KAAK,CAAC4D,KAAK,CAAC,CAAAA,KAAK,aAALA,KAAK,wBAAAuB,gBAAA,GAALvB,KAAK,CAAEb,QAAQ,cAAAoC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBb,IAAI,cAAAc,qBAAA,uBAArBA,qBAAA,CAAuBH,OAAO,KAAI,sBAAsB,EAAE;UACpE/B,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAE;QACb,CAAC,CAAC;QACF3B,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC,MAAM;MACLJ,SAAS,CAACkB,UAAU,CAACnB,MAAM,CAAC;IAC9B;EACF,CAAC;EAID,oBACET,OAAA;IAAK2E,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAE7B5E,OAAA;MAAK2E,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxB5E,OAAA;QAAK6E,GAAG,EAAEnF,qBAAqB,CAAC,CAAC,CAACoF,IAAK;QAACC,GAAG,EAAErF,qBAAqB,CAAC,CAAC,CAACqF,GAAI;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpH,CAAC,eAENtF,OAAA;MAAK2E,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB5E,OAAA;QAAK2E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5E,OAAA;UAAA4E,QAAA,EAAI;QAAY;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBtF,OAAA;UAAA4E,QAAA,EAAG;QAAoC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eAENtF,OAAA;QAAMuF,QAAQ,EAAE7D,YAAa;QAACiD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACjD5E,OAAA;UAAK2E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B5E,OAAA;YAAOwF,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAAa;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CtF,OAAA;YAAK2E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC5E,OAAA,CAACZ,IAAI;cAACqG,IAAI,EAAC,mBAAmB;cAACd,SAAS,EAAC;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7DtF,OAAA;cACE0F,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,OAAO;cACVvE,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEhB,QAAQ,CAACE,KAAM;cACtBqF,QAAQ,EAAE1E,YAAa;cACvB2E,WAAW,EAAC,mBAAmB;cAC/BlB,SAAS,EAAE,cAAclE,MAAM,CAACF,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;cACvDuF,QAAQ,EAAEjF;YAAU;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACL7E,MAAM,CAACF,KAAK,iBAAIP,OAAA;YAAM2E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAEnE,MAAM,CAACF,KAAK,CAAC,CAAC;UAAC;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eAENtF,OAAA;UAAK2E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B5E,OAAA;YAAOwF,OAAO,EAAC,UAAU;YAAAZ,QAAA,EAAC;UAAQ;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1CtF,OAAA;YAAK2E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC5E,OAAA,CAACZ,IAAI;cAACqG,IAAI,EAAC,kBAAkB;cAACd,SAAS,EAAC;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DtF,OAAA;cACE0F,IAAI,EAAE/E,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCgF,EAAE,EAAC,UAAU;cACbvE,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEhB,QAAQ,CAACG,QAAS;cACzBoF,QAAQ,EAAE1E,YAAa;cACvB2E,WAAW,EAAC,sCAAQ;cACpBlB,SAAS,EAAE,cAAclE,MAAM,CAACD,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;cAC1DsF,QAAQ,EAAEjF;YAAU;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACFtF,OAAA;cACE0F,IAAI,EAAC,QAAQ;cACbf,SAAS,EAAC,sBAAsB;cAChCoB,OAAO,EAAEA,CAAA,KAAMnF,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9C,cAAYA,YAAY,GAAG,eAAe,GAAG,eAAgB;cAC7DmF,QAAQ,EAAEjF,SAAU;cAAA+D,QAAA,eAEpB5E,OAAA,CAACZ,IAAI;gBAACqG,IAAI,EAAE9E,YAAY,GAAG,qBAAqB,GAAG;cAAkB;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACL7E,MAAM,CAACD,QAAQ,iBAAIR,OAAA;YAAM2E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAEnE,MAAM,CAACD,QAAQ,CAAC,CAAC;UAAC;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eAENtF,OAAA;UAAK2E,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnC5E,OAAA;YAAG+C,IAAI,EAAC,uBAAuB;YAAC4B,SAAS,EAAE9D,SAAS,GAAG,eAAe,GAAG,EAAG;YAAA+D,QAAA,EAAC;UAAgB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CAAC,eAENtF,OAAA;UACE0F,IAAI,EAAC,QAAQ;UACbf,SAAS,EAAE,mBAAmB9D,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;UAC3DiF,QAAQ,EAAEjF,SAAU;UAAA+D,QAAA,EAEnB/D,SAAS,gBACRb,OAAA,CAAAE,SAAA;YAAA0E,QAAA,gBACE5E,OAAA;cAAK2E,SAAS,EAAC;YAAS;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,iBAEjC;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAETtF,OAAA;UAAK2E,SAAS,EAAC,cAAc;UAAAC,QAAA,GAAC,yBACL,eAAA5E,OAAA;YAAG+C,IAAI,EAAC,gBAAgB;YAAC4B,SAAS,EAAE9D,SAAS,GAAG,eAAe,GAAG,EAAG;YAAA+D,QAAA,EAAC;UAAc;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAClF,EAAA,CA5QQD,KAAK;EAAA,QAQKd,WAAW,EACMS,cAAc;AAAA;AAAAkG,EAAA,GATzC7F,KAAK;AA8Qd,eAAeA,KAAK;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}