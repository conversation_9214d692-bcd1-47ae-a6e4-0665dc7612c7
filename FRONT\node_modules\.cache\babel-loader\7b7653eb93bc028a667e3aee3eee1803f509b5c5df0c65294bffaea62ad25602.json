{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\user\\\\course\\\\CourseTab.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useNavigate } from 'react-router-dom';\nimport { allCourses } from '../../../services/userService';\nimport NoData from '../../../components/common/NoData';\nimport './Course.css';\nimport { encodeData } from '../../../utils/encodeAndEncode'; // adjust path if needed\nimport { toast } from 'react-toastify';\n\n// Helper function to get currency symbol\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getCurrencySymbol = currency => {\n  switch (currency === null || currency === void 0 ? void 0 : currency.toUpperCase()) {\n    case 'INR':\n      return '₹';\n    case 'USD':\n      return '$';\n    case 'SGD':\n      return 'S$';\n    case 'EUR':\n      return '€';\n    case 'GBP':\n      return '£';\n    default:\n      return '$';\n    // Default to USD symbol\n  }\n};\nfunction CourseTab() {\n  _s();\n  const navigate = useNavigate();\n  const observer = useRef();\n  const [courses, setCourses] = useState([]);\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [isLoading, setIsLoading] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [clickedCourseId, setClickedCourseId] = useState(null);\n  const searchTimeout = useRef(null);\n  const fetchCourses = async (pageNum = 1, reset = false, search = '') => {\n    try {\n      setIsLoading(true);\n      const start = Date.now();\n      const response = await allCourses({\n        page: pageNum,\n        limit: 10,\n        search\n      });\n      console.log(\"Response ------------\", response);\n      const elapsed = Date.now() - start;\n      const delay = 500 - elapsed;\n      const process = () => {\n        var _response$data;\n        if (response.success && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.courses) {\n          const newCourses = response.data.courses.map((course, index) => {\n            var _response$data$metada, _response$data$metada2, _response$data$metada3, _course$course_type;\n            return {\n              id: course.id,\n              image: course.banner_image,\n              title: course.course_name || 'Untitled Course',\n              description: course.course_desc || 'No description provided.',\n              modules: ((_response$data$metada = response.data.metadata[index]) === null || _response$data$metada === void 0 ? void 0 : _response$data$metada.totalModules) || 0,\n              enrolled: ((_response$data$metada2 = response.data.metadata[index]) === null || _response$data$metada2 === void 0 ? void 0 : _response$data$metada2.totalUsers) || 0,\n              duration: ((_response$data$metada3 = response.data.metadata[index]) === null || _response$data$metada3 === void 0 ? void 0 : _response$data$metada3.duration) || '—',\n              rating: course.total_rating || 0,\n              level: course.levels || 'N/A',\n              price: ((_course$course_type = course.course_type) === null || _course$course_type === void 0 ? void 0 : _course$course_type.toLowerCase()) === 'free' ? 'Free' : course.course_price || '0.00',\n              course_type: course.course_type,\n              currency: course.currency || 'USD',\n              course_url: course.course_url || null\n            };\n          });\n          setCourses(prev => reset ? newCourses : [...prev, ...newCourses]);\n          setHasMore(pageNum < response.data.totalPages);\n        } else {\n          setHasMore(false);\n        }\n        setIsLoading(false);\n      };\n      if (delay > 0) {\n        setTimeout(process, delay);\n      } else {\n        process();\n      }\n    } catch (err) {\n      console.error(err);\n      setIsLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchCourses(1, true, searchTerm);\n    setPage(1);\n  }, []);\n  useEffect(() => {\n    if (page > 1) fetchCourses(page, false, searchTerm);\n  }, [page]);\n  const lastCourseRef = useCallback(node => {\n    if (isLoading) return;\n    if (observer.current) observer.current.disconnect();\n    observer.current = new IntersectionObserver(entries => {\n      if (entries[0].isIntersecting && hasMore) {\n        setPage(prev => prev + 1);\n      }\n    });\n    if (node) observer.current.observe(node);\n  }, [isLoading, hasMore]);\n  const handleCourseClick = course => {\n    setClickedCourseId(course.id);\n    const encoded = encodeData({\n      id: course.id\n    }); // Only encode the course ID\n    console.log(\"Encoded ID ------------\", encoded);\n    setTimeout(() => {\n      navigate(`/user/courses/courseDetails/${encodeURIComponent(encoded)}`);\n    }, 400);\n  };\n  const handleSearchChange = e => {\n    const value = e.target.value;\n    setSearchTerm(value);\n    if (searchTimeout.current) clearTimeout(searchTimeout.current);\n    searchTimeout.current = setTimeout(() => {\n      setCourses([]);\n      setPage(1);\n      fetchCourses(1, true, value);\n    }, 500);\n  };\n  const handleCopyUrl = async courseUrl => {\n    try {\n      if (courseUrl) {\n        await navigator.clipboard.writeText(courseUrl);\n        toast.success('Course URL copied to clipboard!', {\n          position: 'top-right',\n          autoClose: 2000\n        });\n      } else {\n        toast.error('Course URL not available', {\n          position: 'top-right',\n          autoClose: 2000\n        });\n      }\n    } catch (error) {\n      console.error('Failed to copy URL:', error);\n      toast.error('Failed to copy URL', {\n        position: 'top-right',\n        autoClose: 2000\n      });\n    }\n  };\n  const handleShareCourse = async course => {\n    try {\n      if (navigator.share && course.course_url) {\n        await navigator.share({\n          title: course.title,\n          text: course.description,\n          url: course.course_url\n        });\n      } else if (course.course_url) {\n        // Fallback to copy URL if native sharing is not available\n        await handleCopyUrl(course.course_url);\n      } else {\n        toast.error('Course URL not available', {\n          position: 'top-right',\n          autoClose: 2000\n        });\n      }\n    } catch (error) {\n      console.error('Failed to share course:', error);\n      // If sharing was cancelled, don't show error\n      if (error.name !== 'AbortError') {\n        toast.error('Failed to share course', {\n          position: 'top-right',\n          autoClose: 2000\n        });\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"course-tab-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mt-2 mb-2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-4 \",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"seach-control position-relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control search-input\",\n            placeholder: \"Search courses...\",\n            value: searchTerm,\n            onChange: handleSearchChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner-border text-primary position-absolute\",\n            style: {\n              width: '1rem',\n              height: '1rem',\n              right: '10px',\n              top: '50%',\n              transform: 'translateY(-50%)'\n            },\n            role: \"status\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"visually-hidden\",\n              children: \"Loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 9\n    }, this), courses.length === 0 && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center w-100\",\n      style: {\n        minHeight: '300px'\n      },\n      children: /*#__PURE__*/_jsxDEV(NoData, {\n        message: \"No courses found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 11\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [courses.map((course, index) => {\n        var _course$course_type2, _course$course_type3;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: index === courses.length - 1 ? lastCourseRef : null,\n          className: \"col-md-6 col-lg-3 mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"course-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"course-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: course.image,\n                alt: course.title,\n                className: \"img-fluid\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"course-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"course-title\",\n                children: course.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"course-description\",\n                children: course.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"course-meta-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"meta-row d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"views-count\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"mdi:eye-outline\",\n                      className: \"meta-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: course.enrolled\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"rating-stars-container\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"mdi:star\",\n                      className: \"star-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"rating-value\",\n                      children: course.rating\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"meta-row d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"course-duration\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"mdi:clock-outline\",\n                      className: \"meta-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [course.modules, \" module\", course.modules !== 1 ? 's' : '']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"course-duration\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"mdi:signal-cellular-outline\",\n                      className: \"meta-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: course.level\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"meta-row d-flex justify-content-center gap-2 mt-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleShareCourse(course),\n                    className: \"btn btn-sm btn-light\",\n                    title: \"Share Course\",\n                    style: {\n                      width: '32px',\n                      height: '32px',\n                      padding: '0',\n                      borderRadius: '6px',\n                      border: '1px solid #e0e0e0',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      transition: 'all 0.2s ease',\n                      backgroundColor: '#f8f9fa'\n                    },\n                    onMouseEnter: e => {\n                      e.target.style.backgroundColor = '#e9ecef';\n                      e.target.style.transform = 'translateY(-1px)';\n                    },\n                    onMouseLeave: e => {\n                      e.target.style.backgroundColor = '#f8f9fa';\n                      e.target.style.transform = 'translateY(0)';\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"mdi:share-variant\",\n                      style: {\n                        fontSize: '14px',\n                        color: '#6c757d'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleCopyUrl(course.course_url),\n                    className: \"btn btn-sm btn-light\",\n                    title: \"Copy Course URL\",\n                    style: {\n                      width: '32px',\n                      height: '32px',\n                      padding: '0',\n                      borderRadius: '6px',\n                      border: '1px solid #e0e0e0',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      transition: 'all 0.2s ease',\n                      backgroundColor: '#f8f9fa'\n                    },\n                    onMouseEnter: e => {\n                      e.target.style.backgroundColor = '#e9ecef';\n                      e.target.style.transform = 'translateY(-1px)';\n                    },\n                    onMouseLeave: e => {\n                      e.target.style.backgroundColor = '#f8f9fa';\n                      e.target.style.transform = 'translateY(0)';\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"mdi:content-copy\",\n                      style: {\n                        fontSize: '14px',\n                        color: '#6c757d'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"course-footer\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center justify-content-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `course-price ${course.price === 'Free' ? 'free-price' : 'paid-price'}`,\n                    children: course.price === 'Free' ? 'Free' : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"price-icon\",\n                        children: getCurrencySymbol(course.currency)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 331,\n                        columnNumber: 29\n                      }, this), course.price]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleShareCourse(course),\n                      className: \"btn btn-sm btn-light\",\n                      title: \"Share Course\",\n                      style: {\n                        width: '36px',\n                        height: '36px',\n                        padding: '0',\n                        borderRadius: '8px',\n                        border: '1px solid #e0e0e0',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        transition: 'all 0.2s ease',\n                        backgroundColor: '#f8f9fa'\n                      },\n                      onMouseEnter: e => {\n                        e.target.style.backgroundColor = '#e9ecef';\n                        e.target.style.transform = 'translateY(-1px)';\n                      },\n                      onMouseLeave: e => {\n                        e.target.style.backgroundColor = '#f8f9fa';\n                        e.target.style.transform = 'translateY(0)';\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:share-variant\",\n                        style: {\n                          fontSize: '16px',\n                          color: '#6c757d'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 365,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleCopyUrl(course.course_url),\n                      className: \"btn btn-sm btn-light\",\n                      title: \"Copy Course URL\",\n                      style: {\n                        width: '36px',\n                        height: '36px',\n                        padding: '0',\n                        borderRadius: '8px',\n                        border: '1px solid #e0e0e0',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        transition: 'all 0.2s ease',\n                        backgroundColor: '#f8f9fa'\n                      },\n                      onMouseEnter: e => {\n                        e.target.style.backgroundColor = '#e9ecef';\n                        e.target.style.transform = 'translateY(-1px)';\n                      },\n                      onMouseLeave: e => {\n                        e.target.style.backgroundColor = '#f8f9fa';\n                        e.target.style.transform = 'translateY(0)';\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:content-copy\",\n                        style: {\n                          fontSize: '16px',\n                          color: '#6c757d'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 397,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleCourseClick(course),\n                  className: `watch-now-btn btn btn-primary w-100`,\n                  disabled: clickedCourseId === course.id,\n                  style: {\n                    height: '44px',\n                    borderRadius: '8px',\n                    fontWeight: '600',\n                    fontSize: '14px',\n                    transition: 'all 0.2s ease'\n                  },\n                  children: clickedCourseId === course.id ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"spinner-border spinner-border-sm text-light\",\n                    role: \"status\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"visually-hidden\",\n                      children: \"Loading...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      icon: ((_course$course_type2 = course.course_type) === null || _course$course_type2 === void 0 ? void 0 : _course$course_type2.toLowerCase()) === 'free' ? \"mdi:play-circle\" : \"mdi:lock\",\n                      className: \"btn-icon me-2\",\n                      style: {\n                        fontSize: '18px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 27\n                    }, this), ((_course$course_type3 = course.course_type) === null || _course$course_type3 === void 0 ? void 0 : _course$course_type3.toLowerCase()) === 'free' ? 'Watch Now' : 'Enroll Now']\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)\n        }, course.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this);\n      }), isLoading && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-center mt-2\",\n        children: \"Loading more courses...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 7\n  }, this);\n}\n_s(CourseTab, \"FxIM/KI34T4EDmY6U3RkRDXhI7Y=\", false, function () {\n  return [useNavigate];\n});\n_c = CourseTab;\nexport default CourseTab;\nvar _c;\n$RefreshReg$(_c, \"CourseTab\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "Icon", "useNavigate", "allCourses", "NoData", "encodeData", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getCurrencySymbol", "currency", "toUpperCase", "CourseTab", "_s", "navigate", "observer", "courses", "setCourses", "page", "setPage", "hasMore", "setHasMore", "isLoading", "setIsLoading", "searchTerm", "setSearchTerm", "clickedCourseId", "setClickedCourseId", "searchTimeout", "fetchCourses", "pageNum", "reset", "search", "start", "Date", "now", "response", "limit", "console", "log", "elapsed", "delay", "process", "_response$data", "success", "data", "newCourses", "map", "course", "index", "_response$data$metada", "_response$data$metada2", "_response$data$metada3", "_course$course_type", "id", "image", "banner_image", "title", "course_name", "description", "course_desc", "modules", "metadata", "totalModules", "enrolled", "totalUsers", "duration", "rating", "total_rating", "level", "levels", "price", "course_type", "toLowerCase", "course_price", "course_url", "prev", "totalPages", "setTimeout", "err", "error", "lastCourseRef", "node", "current", "disconnect", "IntersectionObserver", "entries", "isIntersecting", "observe", "handleCourseClick", "encoded", "encodeURIComponent", "handleSearchChange", "e", "value", "target", "clearTimeout", "handleCopyUrl", "courseUrl", "navigator", "clipboard", "writeText", "position", "autoClose", "handleShareCourse", "share", "text", "url", "name", "className", "children", "type", "placeholder", "onChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "height", "right", "top", "transform", "role", "length", "minHeight", "message", "_course$course_type2", "_course$course_type3", "ref", "src", "alt", "icon", "onClick", "padding", "borderRadius", "border", "display", "alignItems", "justifyContent", "transition", "backgroundColor", "onMouseEnter", "onMouseLeave", "fontSize", "color", "disabled", "fontWeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/user/course/CourseTab.jsx"], "sourcesContent": ["  import React, { useState, useRef, useEffect, useCallback } from 'react';\n  import { Icon } from '@iconify/react';\n  import { useNavigate } from 'react-router-dom';\n  import { allCourses } from '../../../services/userService';\n  import NoData from '../../../components/common/NoData';\n  import './Course.css';\n  import { encodeData } from '../../../utils/encodeAndEncode'; // adjust path if needed\n  import { toast } from 'react-toastify';\n\n  // Helper function to get currency symbol\n  const getCurrencySymbol = (currency) => {\n    switch (currency?.toUpperCase()) {\n      case 'INR':\n        return '₹';\n      case 'USD':\n        return '$';\n      case 'SGD':\n        return 'S$';\n      case 'EUR':\n        return '€';\n      case 'GBP':\n        return '£';\n      default:\n        return '$'; // Default to USD symbol\n    }\n  };\n\n  function CourseTab() {\n    const navigate = useNavigate();\n    const observer = useRef();\n    const [courses, setCourses] = useState([]);\n    const [page, setPage] = useState(1);\n    const [hasMore, setHasMore] = useState(true);\n    const [isLoading, setIsLoading] = useState(false);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [clickedCourseId, setClickedCourseId] = useState(null);\n    const searchTimeout = useRef(null);\n\n    const fetchCourses = async (pageNum = 1, reset = false, search = '') => {\n      try {\n        setIsLoading(true);\n        const start = Date.now();\n\n        const response = await allCourses({ page: pageNum, limit: 10, search });\n        console.log(\"Response ------------\", response);\n        const elapsed = Date.now() - start;\n        const delay = 500 - elapsed;\n\n        const process = () => {\n          if (response.success && response.data?.courses) {\n            const newCourses = response.data.courses.map((course, index) => ({\n              id: course.id,\n              image: course.banner_image,\n              title: course.course_name || 'Untitled Course',\n              description: course.course_desc || 'No description provided.',\n              modules: response.data.metadata[index]?.totalModules || 0,\n              enrolled: response.data.metadata[index]?.totalUsers || 0,\n              duration: response.data.metadata[index]?.duration || '—',\n              rating: course.total_rating || 0,\n              level: course.levels || 'N/A',\n              price: course.course_type?.toLowerCase() === 'free' ? 'Free' : course.course_price || '0.00',\n              course_type: course.course_type,\n              currency: course.currency || 'USD',\n              course_url: course.course_url || null\n            }));\n            setCourses(prev => reset ? newCourses : [...prev, ...newCourses]);\n            setHasMore(pageNum < response.data.totalPages);\n          } else {\n            setHasMore(false);\n          }\n          setIsLoading(false);\n        };\n\n        if (delay > 0) {\n          setTimeout(process, delay);\n        } else {\n          process();\n        }\n      } catch (err) {\n        console.error(err);\n        setIsLoading(false);\n      }\n    };\n\n    useEffect(() => {\n      fetchCourses(1, true, searchTerm);\n      setPage(1);\n    }, []);\n\n    useEffect(() => {\n      if (page > 1) fetchCourses(page, false, searchTerm);\n    }, [page]);\n\n    const lastCourseRef = useCallback(node => {\n      if (isLoading) return;\n      if (observer.current) observer.current.disconnect();\n      observer.current = new IntersectionObserver(entries => {\n        if (entries[0].isIntersecting && hasMore) {\n          setPage(prev => prev + 1);\n        }\n      });\n      if (node) observer.current.observe(node);\n    }, [isLoading, hasMore]);\n\n    const handleCourseClick = (course) => {\n      setClickedCourseId(course.id);\n      const encoded = encodeData({ id: course.id }); // Only encode the course ID\n      console.log(\"Encoded ID ------------\", encoded);\n    \n      setTimeout(() => {\n        navigate(`/user/courses/courseDetails/${encodeURIComponent(encoded)}`);\n      }, 400);\n    };\n    \n    \n\n    const handleSearchChange = (e) => {\n      const value = e.target.value;\n      setSearchTerm(value);\n      if (searchTimeout.current) clearTimeout(searchTimeout.current);\n      searchTimeout.current = setTimeout(() => {\n        setCourses([]);\n        setPage(1);\n        fetchCourses(1, true, value);\n      }, 500);\n    };\n\n    const handleCopyUrl = async (courseUrl) => {\n      try {\n        if (courseUrl) {\n          await navigator.clipboard.writeText(courseUrl);\n          toast.success('Course URL copied to clipboard!', {\n            position: 'top-right',\n            autoClose: 2000,\n          });\n        } else {\n          toast.error('Course URL not available', {\n            position: 'top-right',\n            autoClose: 2000,\n          });\n        }\n      } catch (error) {\n        console.error('Failed to copy URL:', error);\n        toast.error('Failed to copy URL', {\n          position: 'top-right',\n          autoClose: 2000,\n        });\n      }\n    };\n\n    const handleShareCourse = async (course) => {\n      try {\n        if (navigator.share && course.course_url) {\n          await navigator.share({\n            title: course.title,\n            text: course.description,\n            url: course.course_url,\n          });\n        } else if (course.course_url) {\n          // Fallback to copy URL if native sharing is not available\n          await handleCopyUrl(course.course_url);\n        } else {\n          toast.error('Course URL not available', {\n            position: 'top-right',\n            autoClose: 2000,\n          });\n        }\n      } catch (error) {\n        console.error('Failed to share course:', error);\n        // If sharing was cancelled, don't show error\n        if (error.name !== 'AbortError') {\n          toast.error('Failed to share course', {\n            position: 'top-right',\n            autoClose: 2000,\n          });\n        }\n      }\n    };\n\n    return (\n      <div className=\"course-tab-content\">\n        {/* Search Bar */}\n        <div className=\"row mt-2 mb-2\">\n          <div className=\"col-12 col-md-4 \">\n            <div className=\"seach-control position-relative\">\n              <input\n                type=\"text\"\n                className=\"form-control search-input\"\n                placeholder=\"Search courses...\"\n                value={searchTerm}\n                onChange={handleSearchChange}\n              />\n              {isLoading && (\n                <div\n                  className=\"spinner-border text-primary position-absolute\"\n                  style={{\n                    width: '1rem',\n                    height: '1rem',\n                    right: '10px',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                  }}\n                  role=\"status\"\n                >\n                  <span className=\"visually-hidden\">Loading...</span>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* No Data */}\n        {courses.length === 0 && !isLoading && (\n          <div className=\"d-flex justify-content-center align-items-center w-100\" style={{ minHeight: '300px' }}>\n            <NoData message=\"No courses found.\" />\n          </div>\n        )}\n\n        {/* Courses Grid */}\n        <div className=\"row\">\n          {courses.map((course, index) => (\n            <div\n              key={course.id}\n              ref={index === courses.length - 1 ? lastCourseRef : null}\n              className=\"col-md-6 col-lg-3 mb-2\"\n            >\n              <div className=\"course-card\">\n                <div className=\"course-image\">\n                  <img src={course.image} alt={course.title} className=\"img-fluid\" />\n                </div>\n                <div className=\"course-details\">\n                  <h5 className=\"course-title\">{course.title}</h5>\n                  <p className=\"course-description\">{course.description}</p>\n\n                  <div className=\"course-meta-info\">\n                    <div className=\"meta-row d-flex justify-content-between\">\n                      <div className=\"views-count\">\n                        <Icon icon=\"mdi:eye-outline\" className=\"meta-icon\" />\n                        <span>{course.enrolled}</span>\n                      </div>\n                      <div className=\"rating-stars-container\">\n                        <Icon icon=\"mdi:star\" className=\"star-icon\" />\n                        <span className=\"rating-value\">{course.rating}</span>\n                      </div>\n                    </div>\n\n                    <div className=\"meta-row d-flex justify-content-between\">\n                      <div className=\"course-duration\">\n                        <Icon icon=\"mdi:clock-outline\" className=\"meta-icon\" />\n                        <span>{course.modules} module{course.modules !== 1 ? 's' : ''}</span>\n                      </div>\n                      <div className=\"course-duration\">\n                        <Icon icon=\"mdi:signal-cellular-outline\" className=\"meta-icon\" />\n                        <span>{course.level}</span>\n                      </div>\n                    </div>\n                    \n                    {/* Share and Copy Buttons */}\n                    <div className=\"meta-row d-flex justify-content-center gap-2 mt-2\">\n                      {/* Share Button */}\n                      <button\n                        onClick={() => handleShareCourse(course)}\n                        className=\"btn btn-sm btn-light\"\n                        title=\"Share Course\"\n                        style={{\n                          width: '32px',\n                          height: '32px',\n                          padding: '0',\n                          borderRadius: '6px',\n                          border: '1px solid #e0e0e0',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          transition: 'all 0.2s ease',\n                          backgroundColor: '#f8f9fa'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.target.style.backgroundColor = '#e9ecef';\n                          e.target.style.transform = 'translateY(-1px)';\n                        }}\n                        onMouseLeave={(e) => {\n                          e.target.style.backgroundColor = '#f8f9fa';\n                          e.target.style.transform = 'translateY(0)';\n                        }}\n                      >\n                        <Icon \n                          icon=\"mdi:share-variant\" \n                          style={{ fontSize: '14px', color: '#6c757d' }} \n                        />\n                      </button>\n                      \n                      {/* Copy URL Button */}\n                      <button\n                        onClick={() => handleCopyUrl(course.course_url)}\n                        className=\"btn btn-sm btn-light\"\n                        title=\"Copy Course URL\"\n                        style={{\n                          width: '32px',\n                          height: '32px',\n                          padding: '0',\n                          borderRadius: '6px',\n                          border: '1px solid #e0e0e0',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          transition: 'all 0.2s ease',\n                          backgroundColor: '#f8f9fa'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.target.style.backgroundColor = '#e9ecef';\n                          e.target.style.transform = 'translateY(-1px)';\n                        }}\n                        onMouseLeave={(e) => {\n                          e.target.style.backgroundColor = '#f8f9fa';\n                          e.target.style.transform = 'translateY(0)';\n                        }}\n                      >\n                        <Icon \n                          icon=\"mdi:content-copy\" \n                          style={{ fontSize: '14px', color: '#6c757d' }} \n                        />\n                      </button>\n                    </div>\n                  </div>\n\n                  <div className=\"course-footer\">\n                    <div className=\"d-flex align-items-center justify-content-between mb-2\">\n                      <div className={`course-price ${course.price === 'Free' ? 'free-price' : 'paid-price'}`}>\n                        {course.price === 'Free' ? 'Free' : (\n                          <>\n                            <span className=\"price-icon\">{getCurrencySymbol(course.currency)}</span>\n                            {course.price}\n                          </>\n                        )}\n                      </div>\n                      \n                      {/* Action Buttons */}\n                      <div className=\"d-flex gap-2\">\n                        {/* Share Button */}\n                        <button\n                          onClick={() => handleShareCourse(course)}\n                          className=\"btn btn-sm btn-light\"\n                          title=\"Share Course\"\n                          style={{\n                            width: '36px',\n                            height: '36px',\n                            padding: '0',\n                            borderRadius: '8px',\n                            border: '1px solid #e0e0e0',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            transition: 'all 0.2s ease',\n                            backgroundColor: '#f8f9fa'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.target.style.backgroundColor = '#e9ecef';\n                            e.target.style.transform = 'translateY(-1px)';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.target.style.backgroundColor = '#f8f9fa';\n                            e.target.style.transform = 'translateY(0)';\n                          }}\n                        >\n                          <Icon \n                            icon=\"mdi:share-variant\" \n                            style={{ fontSize: '16px', color: '#6c757d' }} \n                          />\n                        </button>\n                        \n                        {/* Copy URL Button */}\n                        <button\n                          onClick={() => handleCopyUrl(course.course_url)}\n                          className=\"btn btn-sm btn-light\"\n                          title=\"Copy Course URL\"\n                          style={{\n                            width: '36px',\n                            height: '36px',\n                            padding: '0',\n                            borderRadius: '8px',\n                            border: '1px solid #e0e0e0',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            transition: 'all 0.2s ease',\n                            backgroundColor: '#f8f9fa'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.target.style.backgroundColor = '#e9ecef';\n                            e.target.style.transform = 'translateY(-1px)';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.target.style.backgroundColor = '#f8f9fa';\n                            e.target.style.transform = 'translateY(0)';\n                          }}\n                        >\n                          <Icon \n                            icon=\"mdi:content-copy\" \n                            style={{ fontSize: '16px', color: '#6c757d' }} \n                          />\n                        </button>\n                      </div>\n                    </div>\n                    \n                    {/* Main Action Button */}\n                    <button\n                      onClick={() => handleCourseClick(course)}\n                      className={`watch-now-btn btn btn-primary w-100`}\n                      disabled={clickedCourseId === course.id}\n                      style={{\n                        height: '44px',\n                        borderRadius: '8px',\n                        fontWeight: '600',\n                        fontSize: '14px',\n                        transition: 'all 0.2s ease'\n                      }}\n                    >\n                      {clickedCourseId === course.id ? (\n                        <div className=\"spinner-border spinner-border-sm text-light\" role=\"status\">\n                          <span className=\"visually-hidden\">Loading...</span>\n                        </div>\n                      ) : (\n                        <>\n                          <Icon\n                            icon={course.course_type?.toLowerCase() === 'free' ? \"mdi:play-circle\" : \"mdi:lock\"}\n                            className=\"btn-icon me-2\"\n                            style={{ fontSize: '18px' }}\n                          />\n                          {course.course_type?.toLowerCase() === 'free' ? 'Watch Now' : 'Enroll Now'}\n                        </>\n                      )}\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n          {isLoading && <p className=\"text-center mt-2\">Loading more courses...</p>}\n        </div>\n      </div>\n    );\n  }\n\n  export default CourseTab;\n"], "mappings": ";;AAAE,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,OAAOC,MAAM,MAAM,mCAAmC;AACtD,OAAO,cAAc;AACrB,SAASC,UAAU,QAAQ,gCAAgC,CAAC,CAAC;AAC7D,SAASC,KAAK,QAAQ,gBAAgB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,iBAAiB,GAAIC,QAAQ,IAAK;EACtC,QAAQA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,WAAW,CAAC,CAAC;IAC7B,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,IAAI;IACb,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,GAAG;IACZ;MACE,OAAO,GAAG;IAAE;EAChB;AACF,CAAC;AAED,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGnB,MAAM,CAAC,CAAC;EACzB,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuB,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAMiC,aAAa,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAElC,MAAMiC,YAAY,GAAG,MAAAA,CAAOC,OAAO,GAAG,CAAC,EAAEC,KAAK,GAAG,KAAK,EAAEC,MAAM,GAAG,EAAE,KAAK;IACtE,IAAI;MACFT,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMU,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAExB,MAAMC,QAAQ,GAAG,MAAMnC,UAAU,CAAC;QAAEiB,IAAI,EAAEY,OAAO;QAAEO,KAAK,EAAE,EAAE;QAAEL;MAAO,CAAC,CAAC;MACvEM,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEH,QAAQ,CAAC;MAC9C,MAAMI,OAAO,GAAGN,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,KAAK;MAClC,MAAMQ,KAAK,GAAG,GAAG,GAAGD,OAAO;MAE3B,MAAME,OAAO,GAAGA,CAAA,KAAM;QAAA,IAAAC,cAAA;QACpB,IAAIP,QAAQ,CAACQ,OAAO,KAAAD,cAAA,GAAIP,QAAQ,CAACS,IAAI,cAAAF,cAAA,eAAbA,cAAA,CAAe3B,OAAO,EAAE;UAC9C,MAAM8B,UAAU,GAAGV,QAAQ,CAACS,IAAI,CAAC7B,OAAO,CAAC+B,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK;YAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,mBAAA;YAAA,OAAM;cAC/DC,EAAE,EAAEN,MAAM,CAACM,EAAE;cACbC,KAAK,EAAEP,MAAM,CAACQ,YAAY;cAC1BC,KAAK,EAAET,MAAM,CAACU,WAAW,IAAI,iBAAiB;cAC9CC,WAAW,EAAEX,MAAM,CAACY,WAAW,IAAI,0BAA0B;cAC7DC,OAAO,EAAE,EAAAX,qBAAA,GAAAd,QAAQ,CAACS,IAAI,CAACiB,QAAQ,CAACb,KAAK,CAAC,cAAAC,qBAAA,uBAA7BA,qBAAA,CAA+Ba,YAAY,KAAI,CAAC;cACzDC,QAAQ,EAAE,EAAAb,sBAAA,GAAAf,QAAQ,CAACS,IAAI,CAACiB,QAAQ,CAACb,KAAK,CAAC,cAAAE,sBAAA,uBAA7BA,sBAAA,CAA+Bc,UAAU,KAAI,CAAC;cACxDC,QAAQ,EAAE,EAAAd,sBAAA,GAAAhB,QAAQ,CAACS,IAAI,CAACiB,QAAQ,CAACb,KAAK,CAAC,cAAAG,sBAAA,uBAA7BA,sBAAA,CAA+Bc,QAAQ,KAAI,GAAG;cACxDC,MAAM,EAAEnB,MAAM,CAACoB,YAAY,IAAI,CAAC;cAChCC,KAAK,EAAErB,MAAM,CAACsB,MAAM,IAAI,KAAK;cAC7BC,KAAK,EAAE,EAAAlB,mBAAA,GAAAL,MAAM,CAACwB,WAAW,cAAAnB,mBAAA,uBAAlBA,mBAAA,CAAoBoB,WAAW,CAAC,CAAC,MAAK,MAAM,GAAG,MAAM,GAAGzB,MAAM,CAAC0B,YAAY,IAAI,MAAM;cAC5FF,WAAW,EAAExB,MAAM,CAACwB,WAAW;cAC/B9D,QAAQ,EAAEsC,MAAM,CAACtC,QAAQ,IAAI,KAAK;cAClCiE,UAAU,EAAE3B,MAAM,CAAC2B,UAAU,IAAI;YACnC,CAAC;UAAA,CAAC,CAAC;UACH1D,UAAU,CAAC2D,IAAI,IAAI7C,KAAK,GAAGe,UAAU,GAAG,CAAC,GAAG8B,IAAI,EAAE,GAAG9B,UAAU,CAAC,CAAC;UACjEzB,UAAU,CAACS,OAAO,GAAGM,QAAQ,CAACS,IAAI,CAACgC,UAAU,CAAC;QAChD,CAAC,MAAM;UACLxD,UAAU,CAAC,KAAK,CAAC;QACnB;QACAE,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC;MAED,IAAIkB,KAAK,GAAG,CAAC,EAAE;QACbqC,UAAU,CAACpC,OAAO,EAAED,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLC,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACZzC,OAAO,CAAC0C,KAAK,CAACD,GAAG,CAAC;MAClBxD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED1B,SAAS,CAAC,MAAM;IACdgC,YAAY,CAAC,CAAC,EAAE,IAAI,EAAEL,UAAU,CAAC;IACjCL,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAENtB,SAAS,CAAC,MAAM;IACd,IAAIqB,IAAI,GAAG,CAAC,EAAEW,YAAY,CAACX,IAAI,EAAE,KAAK,EAAEM,UAAU,CAAC;EACrD,CAAC,EAAE,CAACN,IAAI,CAAC,CAAC;EAEV,MAAM+D,aAAa,GAAGnF,WAAW,CAACoF,IAAI,IAAI;IACxC,IAAI5D,SAAS,EAAE;IACf,IAAIP,QAAQ,CAACoE,OAAO,EAAEpE,QAAQ,CAACoE,OAAO,CAACC,UAAU,CAAC,CAAC;IACnDrE,QAAQ,CAACoE,OAAO,GAAG,IAAIE,oBAAoB,CAACC,OAAO,IAAI;MACrD,IAAIA,OAAO,CAAC,CAAC,CAAC,CAACC,cAAc,IAAInE,OAAO,EAAE;QACxCD,OAAO,CAACyD,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MAC3B;IACF,CAAC,CAAC;IACF,IAAIM,IAAI,EAAEnE,QAAQ,CAACoE,OAAO,CAACK,OAAO,CAACN,IAAI,CAAC;EAC1C,CAAC,EAAE,CAAC5D,SAAS,EAAEF,OAAO,CAAC,CAAC;EAExB,MAAMqE,iBAAiB,GAAIzC,MAAM,IAAK;IACpCrB,kBAAkB,CAACqB,MAAM,CAACM,EAAE,CAAC;IAC7B,MAAMoC,OAAO,GAAGvF,UAAU,CAAC;MAAEmD,EAAE,EAAEN,MAAM,CAACM;IAAG,CAAC,CAAC,CAAC,CAAC;IAC/ChB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmD,OAAO,CAAC;IAE/CZ,UAAU,CAAC,MAAM;MACfhE,QAAQ,CAAC,+BAA+B6E,kBAAkB,CAACD,OAAO,CAAC,EAAE,CAAC;IACxE,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAID,MAAME,kBAAkB,GAAIC,CAAC,IAAK;IAChC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BrE,aAAa,CAACqE,KAAK,CAAC;IACpB,IAAIlE,aAAa,CAACuD,OAAO,EAAEa,YAAY,CAACpE,aAAa,CAACuD,OAAO,CAAC;IAC9DvD,aAAa,CAACuD,OAAO,GAAGL,UAAU,CAAC,MAAM;MACvC7D,UAAU,CAAC,EAAE,CAAC;MACdE,OAAO,CAAC,CAAC,CAAC;MACVU,YAAY,CAAC,CAAC,EAAE,IAAI,EAAEiE,KAAK,CAAC;IAC9B,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMG,aAAa,GAAG,MAAOC,SAAS,IAAK;IACzC,IAAI;MACF,IAAIA,SAAS,EAAE;QACb,MAAMC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,SAAS,CAAC;QAC9C9F,KAAK,CAACwC,OAAO,CAAC,iCAAiC,EAAE;UAC/C0D,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ,CAAC,MAAM;QACLnG,KAAK,CAAC4E,KAAK,CAAC,0BAA0B,EAAE;UACtCsB,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C5E,KAAK,CAAC4E,KAAK,CAAC,oBAAoB,EAAE;QAChCsB,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAOxD,MAAM,IAAK;IAC1C,IAAI;MACF,IAAImD,SAAS,CAACM,KAAK,IAAIzD,MAAM,CAAC2B,UAAU,EAAE;QACxC,MAAMwB,SAAS,CAACM,KAAK,CAAC;UACpBhD,KAAK,EAAET,MAAM,CAACS,KAAK;UACnBiD,IAAI,EAAE1D,MAAM,CAACW,WAAW;UACxBgD,GAAG,EAAE3D,MAAM,CAAC2B;QACd,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI3B,MAAM,CAAC2B,UAAU,EAAE;QAC5B;QACA,MAAMsB,aAAa,CAACjD,MAAM,CAAC2B,UAAU,CAAC;MACxC,CAAC,MAAM;QACLvE,KAAK,CAAC4E,KAAK,CAAC,0BAA0B,EAAE;UACtCsB,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;MACA,IAAIA,KAAK,CAAC4B,IAAI,KAAK,YAAY,EAAE;QAC/BxG,KAAK,CAAC4E,KAAK,CAAC,wBAAwB,EAAE;UACpCsB,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,oBACEjG,OAAA;IAAKuG,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAEjCxG,OAAA;MAAKuG,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BxG,OAAA;QAAKuG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BxG,OAAA;UAAKuG,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9CxG,OAAA;YACEyG,IAAI,EAAC,MAAM;YACXF,SAAS,EAAC,2BAA2B;YACrCG,WAAW,EAAC,mBAAmB;YAC/BlB,KAAK,EAAEtE,UAAW;YAClByF,QAAQ,EAAErB;UAAmB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,EACD/F,SAAS,iBACRhB,OAAA;YACEuG,SAAS,EAAC,+CAA+C;YACzDS,KAAK,EAAE;cACLC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdC,KAAK,EAAE,MAAM;cACbC,GAAG,EAAE,KAAK;cACVC,SAAS,EAAE;YACb,CAAE;YACFC,IAAI,EAAC,QAAQ;YAAAd,QAAA,eAEbxG,OAAA;cAAMuG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLrG,OAAO,CAAC6G,MAAM,KAAK,CAAC,IAAI,CAACvG,SAAS,iBACjChB,OAAA;MAAKuG,SAAS,EAAC,wDAAwD;MAACS,KAAK,EAAE;QAAEQ,SAAS,EAAE;MAAQ,CAAE;MAAAhB,QAAA,eACpGxG,OAAA,CAACJ,MAAM;QAAC6H,OAAO,EAAC;MAAmB;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CACN,eAGD/G,OAAA;MAAKuG,SAAS,EAAC,KAAK;MAAAC,QAAA,GACjB9F,OAAO,CAAC+B,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK;QAAA,IAAA+E,oBAAA,EAAAC,oBAAA;QAAA,oBACzB3H,OAAA;UAEE4H,GAAG,EAAEjF,KAAK,KAAKjC,OAAO,CAAC6G,MAAM,GAAG,CAAC,GAAG5C,aAAa,GAAG,IAAK;UACzD4B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eAElCxG,OAAA;YAAKuG,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BxG,OAAA;cAAKuG,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BxG,OAAA;gBAAK6H,GAAG,EAAEnF,MAAM,CAACO,KAAM;gBAAC6E,GAAG,EAAEpF,MAAM,CAACS,KAAM;gBAACoD,SAAS,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACN/G,OAAA;cAAKuG,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BxG,OAAA;gBAAIuG,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE9D,MAAM,CAACS;cAAK;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChD/G,OAAA;gBAAGuG,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAE9D,MAAM,CAACW;cAAW;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE1D/G,OAAA;gBAAKuG,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BxG,OAAA;kBAAKuG,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtDxG,OAAA;oBAAKuG,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BxG,OAAA,CAACP,IAAI;sBAACsI,IAAI,EAAC,iBAAiB;sBAACxB,SAAS,EAAC;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrD/G,OAAA;sBAAAwG,QAAA,EAAO9D,MAAM,CAACgB;oBAAQ;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACN/G,OAAA;oBAAKuG,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCxG,OAAA,CAACP,IAAI;sBAACsI,IAAI,EAAC,UAAU;sBAACxB,SAAS,EAAC;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9C/G,OAAA;sBAAMuG,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAE9D,MAAM,CAACmB;oBAAM;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN/G,OAAA;kBAAKuG,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtDxG,OAAA;oBAAKuG,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9BxG,OAAA,CAACP,IAAI;sBAACsI,IAAI,EAAC,mBAAmB;sBAACxB,SAAS,EAAC;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvD/G,OAAA;sBAAAwG,QAAA,GAAO9D,MAAM,CAACa,OAAO,EAAC,SAAO,EAACb,MAAM,CAACa,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;oBAAA;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACN/G,OAAA;oBAAKuG,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9BxG,OAAA,CAACP,IAAI;sBAACsI,IAAI,EAAC,6BAA6B;sBAACxB,SAAS,EAAC;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjE/G,OAAA;sBAAAwG,QAAA,EAAO9D,MAAM,CAACqB;oBAAK;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN/G,OAAA;kBAAKuG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,gBAEhExG,OAAA;oBACEgI,OAAO,EAAEA,CAAA,KAAM9B,iBAAiB,CAACxD,MAAM,CAAE;oBACzC6D,SAAS,EAAC,sBAAsB;oBAChCpD,KAAK,EAAC,cAAc;oBACpB6D,KAAK,EAAE;sBACLC,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACde,OAAO,EAAE,GAAG;sBACZC,YAAY,EAAE,KAAK;sBACnBC,MAAM,EAAE,mBAAmB;sBAC3BC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxBC,UAAU,EAAE,eAAe;sBAC3BC,eAAe,EAAE;oBACnB,CAAE;oBACFC,YAAY,EAAGlD,CAAC,IAAK;sBACnBA,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACwB,eAAe,GAAG,SAAS;sBAC1CjD,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACK,SAAS,GAAG,kBAAkB;oBAC/C,CAAE;oBACFqB,YAAY,EAAGnD,CAAC,IAAK;sBACnBA,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACwB,eAAe,GAAG,SAAS;sBAC1CjD,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACK,SAAS,GAAG,eAAe;oBAC5C,CAAE;oBAAAb,QAAA,eAEFxG,OAAA,CAACP,IAAI;sBACHsI,IAAI,EAAC,mBAAmB;sBACxBf,KAAK,EAAE;wBAAE2B,QAAQ,EAAE,MAAM;wBAAEC,KAAK,EAAE;sBAAU;oBAAE;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eAGT/G,OAAA;oBACEgI,OAAO,EAAEA,CAAA,KAAMrC,aAAa,CAACjD,MAAM,CAAC2B,UAAU,CAAE;oBAChDkC,SAAS,EAAC,sBAAsB;oBAChCpD,KAAK,EAAC,iBAAiB;oBACvB6D,KAAK,EAAE;sBACLC,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACde,OAAO,EAAE,GAAG;sBACZC,YAAY,EAAE,KAAK;sBACnBC,MAAM,EAAE,mBAAmB;sBAC3BC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxBC,UAAU,EAAE,eAAe;sBAC3BC,eAAe,EAAE;oBACnB,CAAE;oBACFC,YAAY,EAAGlD,CAAC,IAAK;sBACnBA,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACwB,eAAe,GAAG,SAAS;sBAC1CjD,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACK,SAAS,GAAG,kBAAkB;oBAC/C,CAAE;oBACFqB,YAAY,EAAGnD,CAAC,IAAK;sBACnBA,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACwB,eAAe,GAAG,SAAS;sBAC1CjD,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACK,SAAS,GAAG,eAAe;oBAC5C,CAAE;oBAAAb,QAAA,eAEFxG,OAAA,CAACP,IAAI;sBACHsI,IAAI,EAAC,kBAAkB;sBACvBf,KAAK,EAAE;wBAAE2B,QAAQ,EAAE,MAAM;wBAAEC,KAAK,EAAE;sBAAU;oBAAE;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN/G,OAAA;gBAAKuG,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BxG,OAAA;kBAAKuG,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrExG,OAAA;oBAAKuG,SAAS,EAAE,gBAAgB7D,MAAM,CAACuB,KAAK,KAAK,MAAM,GAAG,YAAY,GAAG,YAAY,EAAG;oBAAAuC,QAAA,EACrF9D,MAAM,CAACuB,KAAK,KAAK,MAAM,GAAG,MAAM,gBAC/BjE,OAAA,CAAAE,SAAA;sBAAAsG,QAAA,gBACExG,OAAA;wBAAMuG,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAErG,iBAAiB,CAACuC,MAAM,CAACtC,QAAQ;sBAAC;wBAAAwG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EACvErE,MAAM,CAACuB,KAAK;oBAAA,eACb;kBACH;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAGN/G,OAAA;oBAAKuG,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAE3BxG,OAAA;sBACEgI,OAAO,EAAEA,CAAA,KAAM9B,iBAAiB,CAACxD,MAAM,CAAE;sBACzC6D,SAAS,EAAC,sBAAsB;sBAChCpD,KAAK,EAAC,cAAc;sBACpB6D,KAAK,EAAE;wBACLC,KAAK,EAAE,MAAM;wBACbC,MAAM,EAAE,MAAM;wBACde,OAAO,EAAE,GAAG;wBACZC,YAAY,EAAE,KAAK;wBACnBC,MAAM,EAAE,mBAAmB;wBAC3BC,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBC,cAAc,EAAE,QAAQ;wBACxBC,UAAU,EAAE,eAAe;wBAC3BC,eAAe,EAAE;sBACnB,CAAE;sBACFC,YAAY,EAAGlD,CAAC,IAAK;wBACnBA,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACwB,eAAe,GAAG,SAAS;wBAC1CjD,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACK,SAAS,GAAG,kBAAkB;sBAC/C,CAAE;sBACFqB,YAAY,EAAGnD,CAAC,IAAK;wBACnBA,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACwB,eAAe,GAAG,SAAS;wBAC1CjD,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACK,SAAS,GAAG,eAAe;sBAC5C,CAAE;sBAAAb,QAAA,eAEFxG,OAAA,CAACP,IAAI;wBACHsI,IAAI,EAAC,mBAAmB;wBACxBf,KAAK,EAAE;0BAAE2B,QAAQ,EAAE,MAAM;0BAAEC,KAAK,EAAE;wBAAU;sBAAE;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC,eAGT/G,OAAA;sBACEgI,OAAO,EAAEA,CAAA,KAAMrC,aAAa,CAACjD,MAAM,CAAC2B,UAAU,CAAE;sBAChDkC,SAAS,EAAC,sBAAsB;sBAChCpD,KAAK,EAAC,iBAAiB;sBACvB6D,KAAK,EAAE;wBACLC,KAAK,EAAE,MAAM;wBACbC,MAAM,EAAE,MAAM;wBACde,OAAO,EAAE,GAAG;wBACZC,YAAY,EAAE,KAAK;wBACnBC,MAAM,EAAE,mBAAmB;wBAC3BC,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBC,cAAc,EAAE,QAAQ;wBACxBC,UAAU,EAAE,eAAe;wBAC3BC,eAAe,EAAE;sBACnB,CAAE;sBACFC,YAAY,EAAGlD,CAAC,IAAK;wBACnBA,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACwB,eAAe,GAAG,SAAS;wBAC1CjD,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACK,SAAS,GAAG,kBAAkB;sBAC/C,CAAE;sBACFqB,YAAY,EAAGnD,CAAC,IAAK;wBACnBA,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACwB,eAAe,GAAG,SAAS;wBAC1CjD,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACK,SAAS,GAAG,eAAe;sBAC5C,CAAE;sBAAAb,QAAA,eAEFxG,OAAA,CAACP,IAAI;wBACHsI,IAAI,EAAC,kBAAkB;wBACvBf,KAAK,EAAE;0BAAE2B,QAAQ,EAAE,MAAM;0BAAEC,KAAK,EAAE;wBAAU;sBAAE;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN/G,OAAA;kBACEgI,OAAO,EAAEA,CAAA,KAAM7C,iBAAiB,CAACzC,MAAM,CAAE;kBACzC6D,SAAS,EAAE,qCAAsC;kBACjDsC,QAAQ,EAAEzH,eAAe,KAAKsB,MAAM,CAACM,EAAG;kBACxCgE,KAAK,EAAE;oBACLE,MAAM,EAAE,MAAM;oBACdgB,YAAY,EAAE,KAAK;oBACnBY,UAAU,EAAE,KAAK;oBACjBH,QAAQ,EAAE,MAAM;oBAChBJ,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,EAEDpF,eAAe,KAAKsB,MAAM,CAACM,EAAE,gBAC5BhD,OAAA;oBAAKuG,SAAS,EAAC,6CAA6C;oBAACe,IAAI,EAAC,QAAQ;oBAAAd,QAAA,eACxExG,OAAA;sBAAMuG,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,gBAEN/G,OAAA,CAAAE,SAAA;oBAAAsG,QAAA,gBACExG,OAAA,CAACP,IAAI;sBACHsI,IAAI,EAAE,EAAAL,oBAAA,GAAAhF,MAAM,CAACwB,WAAW,cAAAwD,oBAAA,uBAAlBA,oBAAA,CAAoBvD,WAAW,CAAC,CAAC,MAAK,MAAM,GAAG,iBAAiB,GAAG,UAAW;sBACpFoC,SAAS,EAAC,eAAe;sBACzBS,KAAK,EAAE;wBAAE2B,QAAQ,EAAE;sBAAO;oBAAE;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC,EACD,EAAAY,oBAAA,GAAAjF,MAAM,CAACwB,WAAW,cAAAyD,oBAAA,uBAAlBA,oBAAA,CAAoBxD,WAAW,CAAC,CAAC,MAAK,MAAM,GAAG,WAAW,GAAG,YAAY;kBAAA,eAC1E;gBACH;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GApNDrE,MAAM,CAACM,EAAE;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqNX,CAAC;MAAA,CACP,CAAC,EACD/F,SAAS,iBAAIhB,OAAA;QAAGuG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAuB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACxG,EAAA,CA9ZQD,SAAS;EAAA,QACCZ,WAAW;AAAA;AAAAqJ,EAAA,GADrBzI,SAAS;AAgalB,eAAeA,SAAS;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}