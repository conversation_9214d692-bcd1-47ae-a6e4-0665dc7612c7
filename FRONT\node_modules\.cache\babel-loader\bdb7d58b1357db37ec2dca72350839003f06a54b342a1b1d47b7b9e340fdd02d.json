{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\user\\\\course\\\\CourseTab.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useNavigate } from 'react-router-dom';\nimport { allCourses } from '../../../services/userService';\nimport NoData from '../../../components/common/NoData';\nimport './Course.css';\nimport { encodeData } from '../../../utils/encodeAndEncode'; // adjust path if needed\nimport { toast } from 'react-toastify';\n\n// Helper function to get currency symbol\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getCurrencySymbol = currency => {\n  switch (currency === null || currency === void 0 ? void 0 : currency.toUpperCase()) {\n    case 'INR':\n      return '₹';\n    case 'USD':\n      return '$';\n    case 'SGD':\n      return 'S$';\n    case 'EUR':\n      return '€';\n    case 'GBP':\n      return '£';\n    default:\n      return '$';\n    // Default to USD symbol\n  }\n};\nfunction CourseTab() {\n  _s();\n  const navigate = useNavigate();\n  const observer = useRef();\n  const [courses, setCourses] = useState([]);\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [isLoading, setIsLoading] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [clickedCourseId, setClickedCourseId] = useState(null);\n  const searchTimeout = useRef(null);\n  const fetchCourses = async (pageNum = 1, reset = false, search = '') => {\n    try {\n      setIsLoading(true);\n      const start = Date.now();\n      const response = await allCourses({\n        page: pageNum,\n        limit: 10,\n        search\n      });\n      console.log(\"Response ------------\", response);\n      const elapsed = Date.now() - start;\n      const delay = 500 - elapsed;\n      const process = () => {\n        var _response$data;\n        if (response.success && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.courses) {\n          const newCourses = response.data.courses.map((course, index) => {\n            var _response$data$metada, _response$data$metada2, _response$data$metada3, _course$course_type;\n            return {\n              id: course.id,\n              image: course.banner_image,\n              title: course.course_name || 'Untitled Course',\n              description: course.course_desc || 'No description provided.',\n              modules: ((_response$data$metada = response.data.metadata[index]) === null || _response$data$metada === void 0 ? void 0 : _response$data$metada.totalModules) || 0,\n              enrolled: ((_response$data$metada2 = response.data.metadata[index]) === null || _response$data$metada2 === void 0 ? void 0 : _response$data$metada2.totalUsers) || 0,\n              duration: ((_response$data$metada3 = response.data.metadata[index]) === null || _response$data$metada3 === void 0 ? void 0 : _response$data$metada3.duration) || '—',\n              rating: course.total_rating || 0,\n              level: course.levels || 'N/A',\n              price: ((_course$course_type = course.course_type) === null || _course$course_type === void 0 ? void 0 : _course$course_type.toLowerCase()) === 'free' ? 'Free' : course.course_price || '0.00',\n              course_type: course.course_type,\n              currency: course.currency || 'USD'\n            };\n          });\n          setCourses(prev => reset ? newCourses : [...prev, ...newCourses]);\n          setHasMore(pageNum < response.data.totalPages);\n        } else {\n          setHasMore(false);\n        }\n        setIsLoading(false);\n      };\n      if (delay > 0) {\n        setTimeout(process, delay);\n      } else {\n        process();\n      }\n    } catch (err) {\n      console.error(err);\n      setIsLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchCourses(1, true, searchTerm);\n    setPage(1);\n  }, []);\n  useEffect(() => {\n    if (page > 1) fetchCourses(page, false, searchTerm);\n  }, [page]);\n  const lastCourseRef = useCallback(node => {\n    if (isLoading) return;\n    if (observer.current) observer.current.disconnect();\n    observer.current = new IntersectionObserver(entries => {\n      if (entries[0].isIntersecting && hasMore) {\n        setPage(prev => prev + 1);\n      }\n    });\n    if (node) observer.current.observe(node);\n  }, [isLoading, hasMore]);\n  const handleCourseClick = course => {\n    setClickedCourseId(course.id);\n    const encoded = encodeData({\n      id: course.id\n    }); // Only encode the course ID\n    console.log(\"Encoded ID ------------\", encoded);\n    setTimeout(() => {\n      navigate(`/user/courses/courseDetails/${encodeURIComponent(encoded)}`);\n    }, 400);\n  };\n  const handleSearchChange = e => {\n    const value = e.target.value;\n    setSearchTerm(value);\n    if (searchTimeout.current) clearTimeout(searchTimeout.current);\n    searchTimeout.current = setTimeout(() => {\n      setCourses([]);\n      setPage(1);\n      fetchCourses(1, true, value);\n    }, 500);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"course-tab-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mt-2 mb-2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-4 \",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"seach-control position-relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control search-input\",\n            placeholder: \"Search courses...\",\n            value: searchTerm,\n            onChange: handleSearchChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner-border text-primary position-absolute\",\n            style: {\n              width: '1rem',\n              height: '1rem',\n              right: '10px',\n              top: '50%',\n              transform: 'translateY(-50%)'\n            },\n            role: \"status\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"visually-hidden\",\n              children: \"Loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 9\n    }, this), courses.length === 0 && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center w-100\",\n      style: {\n        minHeight: '300px'\n      },\n      children: /*#__PURE__*/_jsxDEV(NoData, {\n        message: \"No courses found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 11\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [courses.map((course, index) => {\n        var _course$course_type2, _course$course_type3;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: index === courses.length - 1 ? lastCourseRef : null,\n          className: \"col-md-6 col-lg-3 mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"course-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"course-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: course.image,\n                alt: course.title,\n                className: \"img-fluid\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"course-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"course-title\",\n                children: course.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"course-description\",\n                children: course.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"course-meta-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"meta-row d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"views-count\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"mdi:eye-outline\",\n                      className: \"meta-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: course.enrolled\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"rating-stars-container\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"mdi:star\",\n                      className: \"star-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"rating-value\",\n                      children: course.rating\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"meta-row d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"course-duration\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"mdi:clock-outline\",\n                      className: \"meta-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [course.modules, \" module\", course.modules !== 1 ? 's' : '']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"course-duration\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"mdi:signal-cellular-outline\",\n                      className: \"meta-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: course.level\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"course-footer\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `course-price ${course.price === 'Free' ? 'free-price' : 'paid-price'}`,\n                  children: course.price === 'Free' ? 'Free' : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"price-icon\",\n                      children: getCurrencySymbol(course.currency)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 27\n                    }, this), course.price]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleCourseClick(course),\n                  className: `watch-now-btn w-50 btn btn-primary`,\n                  disabled: clickedCourseId === course.id,\n                  children: clickedCourseId === course.id ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"spinner-border spinner-border-sm text-light\",\n                    role: \"status\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"visually-hidden\",\n                      children: \"Loading...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      icon: ((_course$course_type2 = course.course_type) === null || _course$course_type2 === void 0 ? void 0 : _course$course_type2.toLowerCase()) === 'free' ? \"mdi:play-circle\" : \"mdi:lock\",\n                      className: \"btn-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 27\n                    }, this), ((_course$course_type3 = course.course_type) === null || _course$course_type3 === void 0 ? void 0 : _course$course_type3.toLowerCase()) === 'free' ? 'Watch Now' : 'Enroll Now']\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this)\n        }, course.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this);\n      }), isLoading && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-center mt-2\",\n        children: \"Loading more courses...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 7\n  }, this);\n}\n_s(CourseTab, \"FxIM/KI34T4EDmY6U3RkRDXhI7Y=\", false, function () {\n  return [useNavigate];\n});\n_c = CourseTab;\nexport default CourseTab;\nvar _c;\n$RefreshReg$(_c, \"CourseTab\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "Icon", "useNavigate", "allCourses", "NoData", "encodeData", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getCurrencySymbol", "currency", "toUpperCase", "CourseTab", "_s", "navigate", "observer", "courses", "setCourses", "page", "setPage", "hasMore", "setHasMore", "isLoading", "setIsLoading", "searchTerm", "setSearchTerm", "clickedCourseId", "setClickedCourseId", "searchTimeout", "fetchCourses", "pageNum", "reset", "search", "start", "Date", "now", "response", "limit", "console", "log", "elapsed", "delay", "process", "_response$data", "success", "data", "newCourses", "map", "course", "index", "_response$data$metada", "_response$data$metada2", "_response$data$metada3", "_course$course_type", "id", "image", "banner_image", "title", "course_name", "description", "course_desc", "modules", "metadata", "totalModules", "enrolled", "totalUsers", "duration", "rating", "total_rating", "level", "levels", "price", "course_type", "toLowerCase", "course_price", "prev", "totalPages", "setTimeout", "err", "error", "lastCourseRef", "node", "current", "disconnect", "IntersectionObserver", "entries", "isIntersecting", "observe", "handleCourseClick", "encoded", "encodeURIComponent", "handleSearchChange", "e", "value", "target", "clearTimeout", "className", "children", "type", "placeholder", "onChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "height", "right", "top", "transform", "role", "length", "minHeight", "message", "_course$course_type2", "_course$course_type3", "ref", "src", "alt", "icon", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/user/course/CourseTab.jsx"], "sourcesContent": ["  import React, { useState, useRef, useEffect, useCallback } from 'react';\r\n  import { Icon } from '@iconify/react';\r\n  import { useNavigate } from 'react-router-dom';\r\n  import { allCourses } from '../../../services/userService';\r\n  import NoData from '../../../components/common/NoData';\r\n  import './Course.css';\r\n  import { encodeData } from '../../../utils/encodeAndEncode'; // adjust path if needed\r\n  import { toast } from 'react-toastify';\r\n\r\n  // Helper function to get currency symbol\r\n  const getCurrencySymbol = (currency) => {\r\n    switch (currency?.toUpperCase()) {\r\n      case 'INR':\r\n        return '₹';\r\n      case 'USD':\r\n        return '$';\r\n      case 'SGD':\r\n        return 'S$';\r\n      case 'EUR':\r\n        return '€';\r\n      case 'GBP':\r\n        return '£';\r\n      default:\r\n        return '$'; // Default to USD symbol\r\n    }\r\n  };\r\n\r\n  function CourseTab() {\r\n    const navigate = useNavigate();\r\n    const observer = useRef();\r\n    const [courses, setCourses] = useState([]);\r\n    const [page, setPage] = useState(1);\r\n    const [hasMore, setHasMore] = useState(true);\r\n    const [isLoading, setIsLoading] = useState(false);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [clickedCourseId, setClickedCourseId] = useState(null);\r\n    const searchTimeout = useRef(null);\r\n\r\n    const fetchCourses = async (pageNum = 1, reset = false, search = '') => {\r\n      try {\r\n        setIsLoading(true);\r\n        const start = Date.now();\r\n\r\n        const response = await allCourses({ page: pageNum, limit: 10, search });\r\n        console.log(\"Response ------------\", response);\r\n        const elapsed = Date.now() - start;\r\n        const delay = 500 - elapsed;\r\n\r\n        const process = () => {\r\n          if (response.success && response.data?.courses) {\r\n            const newCourses = response.data.courses.map((course, index) => ({\r\n              id: course.id,\r\n              image: course.banner_image,\r\n              title: course.course_name || 'Untitled Course',\r\n              description: course.course_desc || 'No description provided.',\r\n              modules: response.data.metadata[index]?.totalModules || 0,\r\n              enrolled: response.data.metadata[index]?.totalUsers || 0,\r\n              duration: response.data.metadata[index]?.duration || '—',\r\n              rating: course.total_rating || 0,\r\n              level: course.levels || 'N/A',\r\n              price: course.course_type?.toLowerCase() === 'free' ? 'Free' : course.course_price || '0.00',\r\n              course_type: course.course_type,\r\n              currency: course.currency || 'USD'\r\n            }));\r\n            setCourses(prev => reset ? newCourses : [...prev, ...newCourses]);\r\n            setHasMore(pageNum < response.data.totalPages);\r\n          } else {\r\n            setHasMore(false);\r\n          }\r\n          setIsLoading(false);\r\n        };\r\n\r\n        if (delay > 0) {\r\n          setTimeout(process, delay);\r\n        } else {\r\n          process();\r\n        }\r\n      } catch (err) {\r\n        console.error(err);\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    useEffect(() => {\r\n      fetchCourses(1, true, searchTerm);\r\n      setPage(1);\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n      if (page > 1) fetchCourses(page, false, searchTerm);\r\n    }, [page]);\r\n\r\n    const lastCourseRef = useCallback(node => {\r\n      if (isLoading) return;\r\n      if (observer.current) observer.current.disconnect();\r\n      observer.current = new IntersectionObserver(entries => {\r\n        if (entries[0].isIntersecting && hasMore) {\r\n          setPage(prev => prev + 1);\r\n        }\r\n      });\r\n      if (node) observer.current.observe(node);\r\n    }, [isLoading, hasMore]);\r\n\r\n    const handleCourseClick = (course) => {\r\n      setClickedCourseId(course.id);\r\n      const encoded = encodeData({ id: course.id }); // Only encode the course ID\r\n      console.log(\"Encoded ID ------------\", encoded);\r\n    \r\n      setTimeout(() => {\r\n        navigate(`/user/courses/courseDetails/${encodeURIComponent(encoded)}`);\r\n      }, 400);\r\n    };\r\n    \r\n    \r\n\r\n    const handleSearchChange = (e) => {\r\n      const value = e.target.value;\r\n      setSearchTerm(value);\r\n      if (searchTimeout.current) clearTimeout(searchTimeout.current);\r\n      searchTimeout.current = setTimeout(() => {\r\n        setCourses([]);\r\n        setPage(1);\r\n        fetchCourses(1, true, value);\r\n      }, 500);\r\n    };\r\n\r\n    return (\r\n      <div className=\"course-tab-content\">\r\n        {/* Search Bar */}\r\n        <div className=\"row mt-2 mb-2\">\r\n          <div className=\"col-12 col-md-4 \">\r\n            <div className=\"seach-control position-relative\">\r\n              <input\r\n                type=\"text\"\r\n                className=\"form-control search-input\"\r\n                placeholder=\"Search courses...\"\r\n                value={searchTerm}\r\n                onChange={handleSearchChange}\r\n              />\r\n              {isLoading && (\r\n                <div\r\n                  className=\"spinner-border text-primary position-absolute\"\r\n                  style={{\r\n                    width: '1rem',\r\n                    height: '1rem',\r\n                    right: '10px',\r\n                    top: '50%',\r\n                    transform: 'translateY(-50%)',\r\n                  }}\r\n                  role=\"status\"\r\n                >\r\n                  <span className=\"visually-hidden\">Loading...</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* No Data */}\r\n        {courses.length === 0 && !isLoading && (\r\n          <div className=\"d-flex justify-content-center align-items-center w-100\" style={{ minHeight: '300px' }}>\r\n            <NoData message=\"No courses found.\" />\r\n          </div>\r\n        )}\r\n\r\n        {/* Courses Grid */}\r\n        <div className=\"row\">\r\n          {courses.map((course, index) => (\r\n            <div\r\n              key={course.id}\r\n              ref={index === courses.length - 1 ? lastCourseRef : null}\r\n              className=\"col-md-6 col-lg-3 mb-2\"\r\n            >\r\n              <div className=\"course-card\">\r\n                <div className=\"course-image\">\r\n                  <img src={course.image} alt={course.title} className=\"img-fluid\" />\r\n                </div>\r\n                <div className=\"course-details\">\r\n                  <h5 className=\"course-title\">{course.title}</h5>\r\n                  <p className=\"course-description\">{course.description}</p>\r\n\r\n                  <div className=\"course-meta-info\">\r\n                    <div className=\"meta-row d-flex justify-content-between\">\r\n                      <div className=\"views-count\">\r\n                        <Icon icon=\"mdi:eye-outline\" className=\"meta-icon\" />\r\n                        <span>{course.enrolled}</span>\r\n                      </div>\r\n                      <div className=\"rating-stars-container\">\r\n                        <Icon icon=\"mdi:star\" className=\"star-icon\" />\r\n                        <span className=\"rating-value\">{course.rating}</span>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"meta-row d-flex justify-content-between\">\r\n                      <div className=\"course-duration\">\r\n                        <Icon icon=\"mdi:clock-outline\" className=\"meta-icon\" />\r\n                        <span>{course.modules} module{course.modules !== 1 ? 's' : ''}</span>\r\n                      </div>\r\n                      <div className=\"course-duration\">\r\n                        <Icon icon=\"mdi:signal-cellular-outline\" className=\"meta-icon\" />\r\n                        <span>{course.level}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"course-footer\">\r\n                    <div className={`course-price ${course.price === 'Free' ? 'free-price' : 'paid-price'}`}>\r\n                      {course.price === 'Free' ? 'Free' : (\r\n                        <>\r\n                          <span className=\"price-icon\">{getCurrencySymbol(course.currency)}</span>\r\n                          {course.price}\r\n                        </>\r\n                      )}\r\n                    </div>\r\n                    <button\r\n                      onClick={() => handleCourseClick(course)}\r\n                      className={`watch-now-btn w-50 btn btn-primary`}\r\n                      disabled={clickedCourseId === course.id}\r\n                    >\r\n                      {clickedCourseId === course.id ? (\r\n                        <div className=\"spinner-border spinner-border-sm text-light\" role=\"status\">\r\n                          <span className=\"visually-hidden\">Loading...</span>\r\n                        </div>\r\n                      ) : (\r\n                        <>\r\n                          <Icon\r\n                            icon={course.course_type?.toLowerCase() === 'free' ? \"mdi:play-circle\" : \"mdi:lock\"}\r\n                            className=\"btn-icon\"\r\n                          />\r\n                          {course.course_type?.toLowerCase() === 'free' ? 'Watch Now' : 'Enroll Now'}\r\n                        </>\r\n                      )}\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n          {isLoading && <p className=\"text-center mt-2\">Loading more courses...</p>}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  export default CourseTab;\r\n"], "mappings": ";;AAAE,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,OAAOC,MAAM,MAAM,mCAAmC;AACtD,OAAO,cAAc;AACrB,SAASC,UAAU,QAAQ,gCAAgC,CAAC,CAAC;AAC7D,SAASC,KAAK,QAAQ,gBAAgB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,iBAAiB,GAAIC,QAAQ,IAAK;EACtC,QAAQA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,WAAW,CAAC,CAAC;IAC7B,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,IAAI;IACb,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,GAAG;IACZ;MACE,OAAO,GAAG;IAAE;EAChB;AACF,CAAC;AAED,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGnB,MAAM,CAAC,CAAC;EACzB,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuB,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAMiC,aAAa,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAElC,MAAMiC,YAAY,GAAG,MAAAA,CAAOC,OAAO,GAAG,CAAC,EAAEC,KAAK,GAAG,KAAK,EAAEC,MAAM,GAAG,EAAE,KAAK;IACtE,IAAI;MACFT,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMU,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAExB,MAAMC,QAAQ,GAAG,MAAMnC,UAAU,CAAC;QAAEiB,IAAI,EAAEY,OAAO;QAAEO,KAAK,EAAE,EAAE;QAAEL;MAAO,CAAC,CAAC;MACvEM,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEH,QAAQ,CAAC;MAC9C,MAAMI,OAAO,GAAGN,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,KAAK;MAClC,MAAMQ,KAAK,GAAG,GAAG,GAAGD,OAAO;MAE3B,MAAME,OAAO,GAAGA,CAAA,KAAM;QAAA,IAAAC,cAAA;QACpB,IAAIP,QAAQ,CAACQ,OAAO,KAAAD,cAAA,GAAIP,QAAQ,CAACS,IAAI,cAAAF,cAAA,eAAbA,cAAA,CAAe3B,OAAO,EAAE;UAC9C,MAAM8B,UAAU,GAAGV,QAAQ,CAACS,IAAI,CAAC7B,OAAO,CAAC+B,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK;YAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,mBAAA;YAAA,OAAM;cAC/DC,EAAE,EAAEN,MAAM,CAACM,EAAE;cACbC,KAAK,EAAEP,MAAM,CAACQ,YAAY;cAC1BC,KAAK,EAAET,MAAM,CAACU,WAAW,IAAI,iBAAiB;cAC9CC,WAAW,EAAEX,MAAM,CAACY,WAAW,IAAI,0BAA0B;cAC7DC,OAAO,EAAE,EAAAX,qBAAA,GAAAd,QAAQ,CAACS,IAAI,CAACiB,QAAQ,CAACb,KAAK,CAAC,cAAAC,qBAAA,uBAA7BA,qBAAA,CAA+Ba,YAAY,KAAI,CAAC;cACzDC,QAAQ,EAAE,EAAAb,sBAAA,GAAAf,QAAQ,CAACS,IAAI,CAACiB,QAAQ,CAACb,KAAK,CAAC,cAAAE,sBAAA,uBAA7BA,sBAAA,CAA+Bc,UAAU,KAAI,CAAC;cACxDC,QAAQ,EAAE,EAAAd,sBAAA,GAAAhB,QAAQ,CAACS,IAAI,CAACiB,QAAQ,CAACb,KAAK,CAAC,cAAAG,sBAAA,uBAA7BA,sBAAA,CAA+Bc,QAAQ,KAAI,GAAG;cACxDC,MAAM,EAAEnB,MAAM,CAACoB,YAAY,IAAI,CAAC;cAChCC,KAAK,EAAErB,MAAM,CAACsB,MAAM,IAAI,KAAK;cAC7BC,KAAK,EAAE,EAAAlB,mBAAA,GAAAL,MAAM,CAACwB,WAAW,cAAAnB,mBAAA,uBAAlBA,mBAAA,CAAoBoB,WAAW,CAAC,CAAC,MAAK,MAAM,GAAG,MAAM,GAAGzB,MAAM,CAAC0B,YAAY,IAAI,MAAM;cAC5FF,WAAW,EAAExB,MAAM,CAACwB,WAAW;cAC/B9D,QAAQ,EAAEsC,MAAM,CAACtC,QAAQ,IAAI;YAC/B,CAAC;UAAA,CAAC,CAAC;UACHO,UAAU,CAAC0D,IAAI,IAAI5C,KAAK,GAAGe,UAAU,GAAG,CAAC,GAAG6B,IAAI,EAAE,GAAG7B,UAAU,CAAC,CAAC;UACjEzB,UAAU,CAACS,OAAO,GAAGM,QAAQ,CAACS,IAAI,CAAC+B,UAAU,CAAC;QAChD,CAAC,MAAM;UACLvD,UAAU,CAAC,KAAK,CAAC;QACnB;QACAE,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC;MAED,IAAIkB,KAAK,GAAG,CAAC,EAAE;QACboC,UAAU,CAACnC,OAAO,EAAED,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLC,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC,OAAOoC,GAAG,EAAE;MACZxC,OAAO,CAACyC,KAAK,CAACD,GAAG,CAAC;MAClBvD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED1B,SAAS,CAAC,MAAM;IACdgC,YAAY,CAAC,CAAC,EAAE,IAAI,EAAEL,UAAU,CAAC;IACjCL,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAENtB,SAAS,CAAC,MAAM;IACd,IAAIqB,IAAI,GAAG,CAAC,EAAEW,YAAY,CAACX,IAAI,EAAE,KAAK,EAAEM,UAAU,CAAC;EACrD,CAAC,EAAE,CAACN,IAAI,CAAC,CAAC;EAEV,MAAM8D,aAAa,GAAGlF,WAAW,CAACmF,IAAI,IAAI;IACxC,IAAI3D,SAAS,EAAE;IACf,IAAIP,QAAQ,CAACmE,OAAO,EAAEnE,QAAQ,CAACmE,OAAO,CAACC,UAAU,CAAC,CAAC;IACnDpE,QAAQ,CAACmE,OAAO,GAAG,IAAIE,oBAAoB,CAACC,OAAO,IAAI;MACrD,IAAIA,OAAO,CAAC,CAAC,CAAC,CAACC,cAAc,IAAIlE,OAAO,EAAE;QACxCD,OAAO,CAACwD,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MAC3B;IACF,CAAC,CAAC;IACF,IAAIM,IAAI,EAAElE,QAAQ,CAACmE,OAAO,CAACK,OAAO,CAACN,IAAI,CAAC;EAC1C,CAAC,EAAE,CAAC3D,SAAS,EAAEF,OAAO,CAAC,CAAC;EAExB,MAAMoE,iBAAiB,GAAIxC,MAAM,IAAK;IACpCrB,kBAAkB,CAACqB,MAAM,CAACM,EAAE,CAAC;IAC7B,MAAMmC,OAAO,GAAGtF,UAAU,CAAC;MAAEmD,EAAE,EAAEN,MAAM,CAACM;IAAG,CAAC,CAAC,CAAC,CAAC;IAC/ChB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkD,OAAO,CAAC;IAE/CZ,UAAU,CAAC,MAAM;MACf/D,QAAQ,CAAC,+BAA+B4E,kBAAkB,CAACD,OAAO,CAAC,EAAE,CAAC;IACxE,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAID,MAAME,kBAAkB,GAAIC,CAAC,IAAK;IAChC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BpE,aAAa,CAACoE,KAAK,CAAC;IACpB,IAAIjE,aAAa,CAACsD,OAAO,EAAEa,YAAY,CAACnE,aAAa,CAACsD,OAAO,CAAC;IAC9DtD,aAAa,CAACsD,OAAO,GAAGL,UAAU,CAAC,MAAM;MACvC5D,UAAU,CAAC,EAAE,CAAC;MACdE,OAAO,CAAC,CAAC,CAAC;MACVU,YAAY,CAAC,CAAC,EAAE,IAAI,EAAEgE,KAAK,CAAC;IAC9B,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,oBACEvF,OAAA;IAAK0F,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAEjC3F,OAAA;MAAK0F,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B3F,OAAA;QAAK0F,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B3F,OAAA;UAAK0F,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9C3F,OAAA;YACE4F,IAAI,EAAC,MAAM;YACXF,SAAS,EAAC,2BAA2B;YACrCG,WAAW,EAAC,mBAAmB;YAC/BN,KAAK,EAAErE,UAAW;YAClB4E,QAAQ,EAAET;UAAmB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,EACDlF,SAAS,iBACRhB,OAAA;YACE0F,SAAS,EAAC,+CAA+C;YACzDS,KAAK,EAAE;cACLC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdC,KAAK,EAAE,MAAM;cACbC,GAAG,EAAE,KAAK;cACVC,SAAS,EAAE;YACb,CAAE;YACFC,IAAI,EAAC,QAAQ;YAAAd,QAAA,eAEb3F,OAAA;cAAM0F,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLxF,OAAO,CAACgG,MAAM,KAAK,CAAC,IAAI,CAAC1F,SAAS,iBACjChB,OAAA;MAAK0F,SAAS,EAAC,wDAAwD;MAACS,KAAK,EAAE;QAAEQ,SAAS,EAAE;MAAQ,CAAE;MAAAhB,QAAA,eACpG3F,OAAA,CAACJ,MAAM;QAACgH,OAAO,EAAC;MAAmB;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CACN,eAGDlG,OAAA;MAAK0F,SAAS,EAAC,KAAK;MAAAC,QAAA,GACjBjF,OAAO,CAAC+B,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK;QAAA,IAAAkE,oBAAA,EAAAC,oBAAA;QAAA,oBACzB9G,OAAA;UAEE+G,GAAG,EAAEpE,KAAK,KAAKjC,OAAO,CAACgG,MAAM,GAAG,CAAC,GAAGhC,aAAa,GAAG,IAAK;UACzDgB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eAElC3F,OAAA;YAAK0F,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B3F,OAAA;cAAK0F,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B3F,OAAA;gBAAKgH,GAAG,EAAEtE,MAAM,CAACO,KAAM;gBAACgE,GAAG,EAAEvE,MAAM,CAACS,KAAM;gBAACuC,SAAS,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACNlG,OAAA;cAAK0F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B3F,OAAA;gBAAI0F,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEjD,MAAM,CAACS;cAAK;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChDlG,OAAA;gBAAG0F,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEjD,MAAM,CAACW;cAAW;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE1DlG,OAAA;gBAAK0F,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B3F,OAAA;kBAAK0F,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtD3F,OAAA;oBAAK0F,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B3F,OAAA,CAACP,IAAI;sBAACyH,IAAI,EAAC,iBAAiB;sBAACxB,SAAS,EAAC;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrDlG,OAAA;sBAAA2F,QAAA,EAAOjD,MAAM,CAACgB;oBAAQ;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACNlG,OAAA;oBAAK0F,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC3F,OAAA,CAACP,IAAI;sBAACyH,IAAI,EAAC,UAAU;sBAACxB,SAAS,EAAC;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9ClG,OAAA;sBAAM0F,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAEjD,MAAM,CAACmB;oBAAM;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlG,OAAA;kBAAK0F,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtD3F,OAAA;oBAAK0F,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9B3F,OAAA,CAACP,IAAI;sBAACyH,IAAI,EAAC,mBAAmB;sBAACxB,SAAS,EAAC;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvDlG,OAAA;sBAAA2F,QAAA,GAAOjD,MAAM,CAACa,OAAO,EAAC,SAAO,EAACb,MAAM,CAACa,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;oBAAA;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNlG,OAAA;oBAAK0F,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9B3F,OAAA,CAACP,IAAI;sBAACyH,IAAI,EAAC,6BAA6B;sBAACxB,SAAS,EAAC;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjElG,OAAA;sBAAA2F,QAAA,EAAOjD,MAAM,CAACqB;oBAAK;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlG,OAAA;gBAAK0F,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B3F,OAAA;kBAAK0F,SAAS,EAAE,gBAAgBhD,MAAM,CAACuB,KAAK,KAAK,MAAM,GAAG,YAAY,GAAG,YAAY,EAAG;kBAAA0B,QAAA,EACrFjD,MAAM,CAACuB,KAAK,KAAK,MAAM,GAAG,MAAM,gBAC/BjE,OAAA,CAAAE,SAAA;oBAAAyF,QAAA,gBACE3F,OAAA;sBAAM0F,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAExF,iBAAiB,CAACuC,MAAM,CAACtC,QAAQ;oBAAC;sBAAA2F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACvExD,MAAM,CAACuB,KAAK;kBAAA,eACb;gBACH;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNlG,OAAA;kBACEmH,OAAO,EAAEA,CAAA,KAAMjC,iBAAiB,CAACxC,MAAM,CAAE;kBACzCgD,SAAS,EAAE,oCAAqC;kBAChD0B,QAAQ,EAAEhG,eAAe,KAAKsB,MAAM,CAACM,EAAG;kBAAA2C,QAAA,EAEvCvE,eAAe,KAAKsB,MAAM,CAACM,EAAE,gBAC5BhD,OAAA;oBAAK0F,SAAS,EAAC,6CAA6C;oBAACe,IAAI,EAAC,QAAQ;oBAAAd,QAAA,eACxE3F,OAAA;sBAAM0F,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,gBAENlG,OAAA,CAAAE,SAAA;oBAAAyF,QAAA,gBACE3F,OAAA,CAACP,IAAI;sBACHyH,IAAI,EAAE,EAAAL,oBAAA,GAAAnE,MAAM,CAACwB,WAAW,cAAA2C,oBAAA,uBAAlBA,oBAAA,CAAoB1C,WAAW,CAAC,CAAC,MAAK,MAAM,GAAG,iBAAiB,GAAG,UAAW;sBACpFuB,SAAS,EAAC;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,EACD,EAAAY,oBAAA,GAAApE,MAAM,CAACwB,WAAW,cAAA4C,oBAAA,uBAAlBA,oBAAA,CAAoB3C,WAAW,CAAC,CAAC,MAAK,MAAM,GAAG,WAAW,GAAG,YAAY;kBAAA,eAC1E;gBACH;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAlEDxD,MAAM,CAACM,EAAE;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmEX,CAAC;MAAA,CACP,CAAC,EACDlF,SAAS,iBAAIhB,OAAA;QAAG0F,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAuB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC3F,EAAA,CAvNQD,SAAS;EAAA,QACCZ,WAAW;AAAA;AAAA2H,EAAA,GADrB/G,SAAS;AAyNlB,eAAeA,SAAS;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}